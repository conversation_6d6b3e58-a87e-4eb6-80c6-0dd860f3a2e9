<svg width="480" height="149" viewBox="0 0 480 149" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1203_27349)">
<rect x="42" width="438" height="120" rx="8" fill="white"/>
<line x1="52.5057" y1="174" x2="52.5057" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="106.506" y1="174" x2="106.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="159.506" y1="174" x2="159.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="213.506" y1="174" x2="213.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="266.506" y1="174" x2="266.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="319.506" y1="174" x2="319.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="373.506" y1="174" x2="373.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="426.506" y1="174" x2="426.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line y1="28.662" x2="480" y2="28.662" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line y1="57.662" x2="480" y2="57.662" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line y1="86.662" x2="480" y2="86.662" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line y1="115.662" x2="480" y2="115.662" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<g filter="url(#filter0_dd_1203_27349)">
<rect width="40" height="41" transform="translate(60 79)" fill="white"/>
<rect width="40" height="41" transform="translate(60 79)" fill="url(#paint0_linear_1203_27349)"/>
<rect x="60" y="79" width="40" height="41" fill="url(#paint1_linear_1203_27349)"/>
<rect x="60" y="79" width="40" height="2" fill="#1CC73B"/>
</g>
<g filter="url(#filter1_dd_1203_27349)">
<rect width="40" height="76" transform="translate(120 44)" fill="white"/>
<rect width="40" height="76" transform="translate(120 44)" fill="url(#paint2_linear_1203_27349)"/>
<rect x="120" y="44" width="40" height="76" fill="url(#paint3_linear_1203_27349)"/>
<rect x="120" y="44" width="40" height="2" fill="#1CC73B"/>
</g>
<g filter="url(#filter2_dd_1203_27349)">
<rect width="40" height="62" transform="translate(180 58)" fill="white"/>
<rect width="40" height="62" transform="translate(180 58)" fill="url(#paint4_linear_1203_27349)"/>
<rect x="180" y="58" width="40" height="62" fill="url(#paint5_linear_1203_27349)"/>
<rect x="180" y="58" width="40" height="2" fill="#1CC73B"/>
</g>
<g filter="url(#filter3_dd_1203_27349)">
<rect width="40" height="54" transform="translate(240 66)" fill="white"/>
<rect width="40" height="54" transform="translate(240 66)" fill="url(#paint6_linear_1203_27349)"/>
<rect x="240" y="66" width="40" height="54" fill="url(#paint7_linear_1203_27349)"/>
<rect x="240" y="66" width="40" height="2" fill="#1CC73B"/>
</g>
<g filter="url(#filter4_dd_1203_27349)">
<rect width="40" height="86" transform="translate(300 34)" fill="white"/>
<rect width="40" height="86" transform="translate(300 34)" fill="url(#paint8_linear_1203_27349)"/>
<rect x="300" y="34" width="40" height="86" fill="url(#paint9_linear_1203_27349)"/>
<rect x="300" y="34" width="40" height="2" fill="#1CC73B"/>
</g>
<g filter="url(#filter5_dd_1203_27349)">
<rect width="40" height="76" transform="translate(360 44)" fill="white"/>
<rect width="40" height="76" transform="translate(360 44)" fill="url(#paint10_linear_1203_27349)"/>
<rect x="360" y="44" width="40" height="76" fill="url(#paint11_linear_1203_27349)"/>
<rect x="360" y="44" width="40" height="2" fill="#1CC73B"/>
</g>
<g filter="url(#filter6_dd_1203_27349)">
<rect width="40" height="39" transform="translate(420 81)" fill="white"/>
<rect width="40" height="39" transform="translate(420 81)" fill="url(#paint12_linear_1203_27349)"/>
<rect x="420" y="81" width="40" height="39" fill="url(#paint13_linear_1203_27349)"/>
<rect x="420" y="81" width="40" height="2" fill="#1CC73B"/>
</g>
<rect width="480" height="11.493" fill="url(#paint14_linear_1203_27349)"/>
<rect width="480" height="11.493" transform="matrix(1 0 0 -1 0 120.253)" fill="url(#paint15_linear_1203_27349)"/>
</g>
<rect x="42.25" y="0.25" width="437.5" height="119.5" rx="7.75" stroke="#DEDEDE" stroke-width="0.5"/>
<path d="M68.8235 145V136.6H69.9995L73.0235 142.456L76.0235 136.6H77.2115V145H76.2035V138.4L73.3955 143.8H72.6395L69.8315 138.412V145H68.8235ZM81.6357 145.144C81.0757 145.144 80.5717 145.016 80.1237 144.76C79.6757 144.504 79.3197 144.144 79.0557 143.68C78.7997 143.208 78.6717 142.656 78.6717 142.024C78.6717 141.392 78.8037 140.844 79.0677 140.38C79.3317 139.908 79.6877 139.544 80.1357 139.288C80.5917 139.032 81.0997 138.904 81.6597 138.904C82.2197 138.904 82.7237 139.032 83.1717 139.288C83.6197 139.544 83.9717 139.908 84.2277 140.38C84.4917 140.844 84.6237 141.392 84.6237 142.024C84.6237 142.656 84.4917 143.208 84.2277 143.68C83.9637 144.144 83.6037 144.504 83.1477 144.76C82.6997 145.016 82.1957 145.144 81.6357 145.144ZM81.6357 144.28C81.9797 144.28 82.2997 144.196 82.5957 144.028C82.8917 143.86 83.1317 143.608 83.3157 143.272C83.4997 142.936 83.5917 142.52 83.5917 142.024C83.5917 141.528 83.4997 141.112 83.3157 140.776C83.1397 140.44 82.9037 140.188 82.6077 140.02C82.3117 139.852 81.9957 139.768 81.6597 139.768C81.3157 139.768 80.9957 139.852 80.6997 140.02C80.4037 140.188 80.1637 140.44 79.9797 140.776C79.7957 141.112 79.7037 141.528 79.7037 142.024C79.7037 142.52 79.7957 142.936 79.9797 143.272C80.1637 143.608 80.3997 143.86 80.6877 144.028C80.9837 144.196 81.2997 144.28 81.6357 144.28ZM86.0253 145V139.048H86.9373L86.9973 140.116C87.1893 139.74 87.4653 139.444 87.8253 139.228C88.1853 139.012 88.5933 138.904 89.0493 138.904C89.7533 138.904 90.3133 139.12 90.7293 139.552C91.1533 139.976 91.3653 140.632 91.3653 141.52V145H90.3573V141.628C90.3573 140.388 89.8453 139.768 88.8213 139.768C88.3093 139.768 87.8813 139.956 87.5373 140.332C87.2013 140.7 87.0333 141.228 87.0333 141.916V145H86.0253Z" fill="#585858"/>
<path d="M132.948 145V137.428H130.44V136.6H136.464V137.428H133.956V145H132.948ZM139.209 145.144C138.505 145.144 137.945 144.932 137.529 144.508C137.113 144.076 136.905 143.416 136.905 142.528V139.048H137.913V142.42C137.913 143.66 138.421 144.28 139.437 144.28C139.957 144.28 140.385 144.096 140.721 143.728C141.065 143.352 141.237 142.82 141.237 142.132V139.048H142.245V145H141.333L141.261 143.932C141.077 144.308 140.801 144.604 140.433 144.82C140.073 145.036 139.665 145.144 139.209 145.144ZM146.58 145.144C146.012 145.144 145.508 145.016 145.068 144.76C144.628 144.496 144.28 144.132 144.024 143.668C143.776 143.204 143.652 142.656 143.652 142.024C143.652 141.4 143.776 140.856 144.024 140.392C144.272 139.92 144.616 139.556 145.056 139.3C145.504 139.036 146.02 138.904 146.604 138.904C147.18 138.904 147.676 139.036 148.092 139.3C148.516 139.556 148.84 139.896 149.064 140.32C149.288 140.744 149.4 141.2 149.4 141.688C149.4 141.776 149.396 141.864 149.388 141.952C149.388 142.04 149.388 142.14 149.388 142.252H144.648C144.672 142.708 144.776 143.088 144.96 143.392C145.152 143.688 145.388 143.912 145.668 144.064C145.956 144.216 146.26 144.292 146.58 144.292C146.996 144.292 147.344 144.196 147.624 144.004C147.904 143.812 148.108 143.552 148.236 143.224H149.232C149.072 143.776 148.764 144.236 148.308 144.604C147.86 144.964 147.284 145.144 146.58 145.144ZM146.58 139.756C146.1 139.756 145.672 139.904 145.296 140.2C144.928 140.488 144.716 140.912 144.66 141.472H148.404C148.38 140.936 148.196 140.516 147.852 140.212C147.508 139.908 147.084 139.756 146.58 139.756Z" fill="#585858"/>
<path d="M189.936 145.012L187.632 136.6H188.712L190.524 143.812L192.588 136.6H193.704L195.708 143.812L197.544 136.6H198.624L196.272 145H195.12L193.116 138.064L191.064 145L189.936 145.012ZM201.817 145.144C201.249 145.144 200.745 145.016 200.305 144.76C199.865 144.496 199.517 144.132 199.261 143.668C199.013 143.204 198.889 142.656 198.889 142.024C198.889 141.4 199.013 140.856 199.261 140.392C199.509 139.92 199.853 139.556 200.293 139.3C200.741 139.036 201.257 138.904 201.84 138.904C202.417 138.904 202.913 139.036 203.329 139.3C203.753 139.556 204.077 139.896 204.301 140.32C204.525 140.744 204.637 141.2 204.637 141.688C204.637 141.776 204.633 141.864 204.625 141.952C204.625 142.04 204.625 142.14 204.625 142.252H199.885C199.909 142.708 200.013 143.088 200.197 143.392C200.389 143.688 200.625 143.912 200.905 144.064C201.193 144.216 201.497 144.292 201.817 144.292C202.233 144.292 202.581 144.196 202.861 144.004C203.141 143.812 203.345 143.552 203.473 143.224H204.469C204.309 143.776 204.001 144.236 203.545 144.604C203.097 144.964 202.521 145.144 201.817 145.144ZM201.817 139.756C201.337 139.756 200.909 139.904 200.533 140.2C200.165 140.488 199.953 140.912 199.896 141.472H203.641C203.617 140.936 203.433 140.516 203.089 140.212C202.745 139.908 202.321 139.756 201.817 139.756ZM208.673 145.144C208.081 145.144 207.561 145.008 207.113 144.736C206.673 144.464 206.329 144.092 206.081 143.62C205.841 143.148 205.721 142.612 205.721 142.012C205.721 141.412 205.845 140.88 206.093 140.416C206.341 139.944 206.685 139.576 207.125 139.312C207.565 139.04 208.085 138.904 208.685 138.904C209.173 138.904 209.605 139.004 209.981 139.204C210.357 139.404 210.649 139.684 210.857 140.044V136.36H211.865V145H210.953L210.857 144.016C210.665 144.304 210.389 144.564 210.029 144.796C209.669 145.028 209.217 145.144 208.673 145.144ZM208.781 144.268C209.181 144.268 209.533 144.176 209.837 143.992C210.149 143.8 210.389 143.536 210.557 143.2C210.733 142.864 210.821 142.472 210.821 142.024C210.821 141.576 210.733 141.184 210.557 140.848C210.389 140.512 210.149 140.252 209.837 140.068C209.533 139.876 209.181 139.78 208.781 139.78C208.389 139.78 208.037 139.876 207.725 140.068C207.421 140.252 207.181 140.512 207.005 140.848C206.837 141.184 206.753 141.576 206.753 142.024C206.753 142.472 206.837 142.864 207.005 143.2C207.181 143.536 207.421 143.8 207.725 143.992C208.037 144.176 208.389 144.268 208.781 144.268Z" fill="#585858"/>
<path d="M252.631 145V137.428H250.123V136.6H256.147V137.428H253.639V145H252.631ZM257.318 145V136.36H258.326V140.068C258.526 139.7 258.81 139.416 259.178 139.216C259.546 139.008 259.946 138.904 260.378 138.904C261.066 138.904 261.618 139.12 262.034 139.552C262.45 139.976 262.658 140.632 262.658 141.52V145H261.662V141.628C261.662 140.388 261.162 139.768 260.162 139.768C259.642 139.768 259.206 139.956 258.854 140.332C258.502 140.7 258.326 141.228 258.326 141.916V145H257.318ZM266.357 145.144C265.653 145.144 265.093 144.932 264.677 144.508C264.261 144.076 264.053 143.416 264.053 142.528V139.048H265.061V142.42C265.061 143.66 265.569 144.28 266.585 144.28C267.105 144.28 267.533 144.096 267.869 143.728C268.213 143.352 268.385 142.82 268.385 142.132V139.048H269.393V145H268.481L268.409 143.932C268.225 144.308 267.949 144.604 267.581 144.82C267.221 145.036 266.813 145.144 266.357 145.144Z" fill="#585858"/>
<path d="M314.068 145V136.6H319.18V137.428H315.076V140.38H318.568V141.196H315.076V145H314.068ZM320.418 145V139.048H321.33L321.414 140.188C321.598 139.796 321.878 139.484 322.254 139.252C322.63 139.02 323.094 138.904 323.646 138.904V139.96H323.37C323.018 139.96 322.694 140.024 322.398 140.152C322.102 140.272 321.866 140.48 321.69 140.776C321.514 141.072 321.426 141.48 321.426 142V145H320.418ZM325.389 137.728C325.189 137.728 325.021 137.664 324.885 137.536C324.757 137.4 324.693 137.232 324.693 137.032C324.693 136.84 324.757 136.68 324.885 136.552C325.021 136.424 325.189 136.36 325.389 136.36C325.581 136.36 325.745 136.424 325.881 136.552C326.017 136.68 326.085 136.84 326.085 137.032C326.085 137.232 326.017 137.4 325.881 137.536C325.745 137.664 325.581 137.728 325.389 137.728ZM324.885 145V139.048H325.893V145H324.885Z" fill="#585858"/>
<path d="M374.611 145.144C373.995 145.144 373.459 145.032 373.003 144.808C372.547 144.584 372.195 144.272 371.947 143.872C371.699 143.472 371.575 143.008 371.575 142.48H372.631C372.631 142.808 372.707 143.112 372.859 143.392C373.011 143.664 373.231 143.884 373.519 144.052C373.815 144.212 374.179 144.292 374.611 144.292C375.179 144.292 375.619 144.156 375.931 143.884C376.243 143.612 376.399 143.272 376.399 142.864C376.399 142.528 376.327 142.26 376.183 142.06C376.039 141.852 375.843 141.684 375.595 141.556C375.355 141.428 375.075 141.316 374.755 141.22C374.443 141.124 374.115 141.016 373.771 140.896C373.123 140.672 372.643 140.396 372.331 140.068C372.019 139.732 371.863 139.296 371.863 138.76C371.855 138.312 371.959 137.916 372.175 137.572C372.391 137.22 372.695 136.948 373.087 136.756C373.487 136.556 373.959 136.456 374.503 136.456C375.039 136.456 375.503 136.556 375.895 136.756C376.295 136.956 376.603 137.232 376.819 137.584C377.043 137.936 377.159 138.336 377.167 138.784H376.111C376.111 138.552 376.051 138.324 375.931 138.1C375.811 137.868 375.627 137.68 375.379 137.536C375.139 137.392 374.835 137.32 374.467 137.32C374.011 137.312 373.635 137.428 373.339 137.668C373.051 137.908 372.907 138.24 372.907 138.664C372.907 139.024 373.007 139.3 373.207 139.492C373.415 139.684 373.703 139.844 374.071 139.972C374.439 140.092 374.863 140.232 375.343 140.392C375.743 140.536 376.103 140.704 376.423 140.896C376.743 141.088 376.991 141.336 377.167 141.64C377.351 141.944 377.443 142.332 377.443 142.804C377.443 143.204 377.339 143.584 377.131 143.944C376.923 144.296 376.607 144.584 376.183 144.808C375.767 145.032 375.243 145.144 374.611 145.144ZM380.792 145.144C380.296 145.144 379.884 145.06 379.556 144.892C379.228 144.724 378.984 144.5 378.824 144.22C378.664 143.94 378.584 143.636 378.584 143.308C378.584 142.7 378.816 142.232 379.28 141.904C379.744 141.576 380.376 141.412 381.176 141.412H382.784V141.34C382.784 140.82 382.648 140.428 382.376 140.164C382.104 139.892 381.74 139.756 381.284 139.756C380.892 139.756 380.552 139.856 380.264 140.056C379.984 140.248 379.808 140.532 379.736 140.908H378.704C378.744 140.476 378.888 140.112 379.136 139.816C379.392 139.52 379.708 139.296 380.084 139.144C380.46 138.984 380.86 138.904 381.284 138.904C382.116 138.904 382.74 139.128 383.156 139.576C383.58 140.016 383.792 140.604 383.792 141.34V145H382.892L382.832 143.932C382.664 144.268 382.416 144.556 382.088 144.796C381.768 145.028 381.336 145.144 380.792 145.144ZM380.948 144.292C381.332 144.292 381.66 144.192 381.932 143.992C382.212 143.792 382.424 143.532 382.568 143.212C382.712 142.892 382.784 142.556 382.784 142.204V142.192H381.26C380.668 142.192 380.248 142.296 380 142.504C379.76 142.704 379.64 142.956 379.64 143.26C379.64 143.572 379.752 143.824 379.976 144.016C380.208 144.2 380.532 144.292 380.948 144.292ZM387.493 145C386.949 145 386.521 144.868 386.209 144.604C385.897 144.34 385.741 143.864 385.741 143.176V139.9H384.708V139.048H385.741L385.873 137.62H386.749V139.048H388.501V139.9H386.749V143.176C386.749 143.552 386.825 143.808 386.977 143.944C387.129 144.072 387.397 144.136 387.781 144.136H388.405V145H387.493Z" fill="#585858"/>
<path d="M433.228 145.144C432.612 145.144 432.076 145.032 431.62 144.808C431.164 144.584 430.812 144.272 430.564 143.872C430.316 143.472 430.192 143.008 430.192 142.48H431.248C431.248 142.808 431.324 143.112 431.476 143.392C431.628 143.664 431.848 143.884 432.136 144.052C432.432 144.212 432.796 144.292 433.228 144.292C433.796 144.292 434.236 144.156 434.548 143.884C434.86 143.612 435.016 143.272 435.016 142.864C435.016 142.528 434.944 142.26 434.8 142.06C434.656 141.852 434.46 141.684 434.212 141.556C433.972 141.428 433.692 141.316 433.372 141.22C433.06 141.124 432.732 141.016 432.388 140.896C431.74 140.672 431.26 140.396 430.948 140.068C430.636 139.732 430.48 139.296 430.48 138.76C430.472 138.312 430.576 137.916 430.792 137.572C431.008 137.22 431.312 136.948 431.704 136.756C432.104 136.556 432.576 136.456 433.12 136.456C433.656 136.456 434.12 136.556 434.512 136.756C434.912 136.956 435.22 137.232 435.436 137.584C435.66 137.936 435.776 138.336 435.784 138.784H434.728C434.728 138.552 434.668 138.324 434.548 138.1C434.428 137.868 434.244 137.68 433.996 137.536C433.756 137.392 433.452 137.32 433.084 137.32C432.628 137.312 432.252 137.428 431.956 137.668C431.668 137.908 431.524 138.24 431.524 138.664C431.524 139.024 431.624 139.3 431.824 139.492C432.032 139.684 432.32 139.844 432.688 139.972C433.056 140.092 433.48 140.232 433.96 140.392C434.36 140.536 434.72 140.704 435.04 140.896C435.36 141.088 435.608 141.336 435.784 141.64C435.968 141.944 436.06 142.332 436.06 142.804C436.06 143.204 435.956 143.584 435.748 143.944C435.54 144.296 435.224 144.584 434.8 144.808C434.384 145.032 433.86 145.144 433.228 145.144ZM439.602 145.144C438.898 145.144 438.338 144.932 437.922 144.508C437.506 144.076 437.298 143.416 437.298 142.528V139.048H438.306V142.42C438.306 143.66 438.814 144.28 439.83 144.28C440.35 144.28 440.778 144.096 441.114 143.728C441.458 143.352 441.63 142.82 441.63 142.132V139.048H442.638V145H441.726L441.654 143.932C441.47 144.308 441.194 144.604 440.826 144.82C440.466 145.036 440.058 145.144 439.602 145.144ZM444.297 145V139.048H445.209L445.269 140.116C445.461 139.74 445.737 139.444 446.097 139.228C446.457 139.012 446.865 138.904 447.321 138.904C448.025 138.904 448.585 139.12 449.001 139.552C449.425 139.976 449.637 140.632 449.637 141.52V145H448.629V141.628C448.629 140.388 448.117 139.768 447.093 139.768C446.581 139.768 446.153 139.956 445.809 140.332C445.473 140.7 445.305 141.228 445.305 141.916V145H444.297Z" fill="#585858"/>
<path d="M1.716 13V5.728L0.36 6.028V5.332L2.052 4.6H2.736V13H1.716ZM7.78509 13.144C7.04109 13.144 6.39709 12.964 5.85309 12.604C5.31709 12.236 4.90509 11.728 4.61709 11.08C4.32909 10.424 4.18509 9.664 4.18509 8.8C4.18509 7.936 4.32909 7.18 4.61709 6.532C4.90509 5.876 5.31709 5.368 5.85309 5.008C6.39709 4.64 7.04109 4.456 7.78509 4.456C8.52909 4.456 9.16909 4.64 9.70509 5.008C10.2411 5.368 10.6531 5.876 10.9411 6.532C11.2291 7.18 11.3731 7.936 11.3731 8.8C11.3731 9.664 11.2291 10.424 10.9411 11.08C10.6531 11.728 10.2411 12.236 9.70509 12.604C9.16909 12.964 8.52909 13.144 7.78509 13.144ZM7.78509 12.256C8.27309 12.256 8.70909 12.12 9.09309 11.848C9.48509 11.568 9.78909 11.172 10.0051 10.66C10.2291 10.14 10.3411 9.52 10.3411 8.8C10.3411 8.08 10.2291 7.464 10.0051 6.952C9.78909 6.44 9.48509 6.048 9.09309 5.776C8.70909 5.496 8.27309 5.356 7.78509 5.356C7.28909 5.356 6.84909 5.496 6.46509 5.776C6.08109 6.048 5.77709 6.44 5.55309 6.952C5.32909 7.464 5.21709 8.08 5.21709 8.8C5.21709 9.52 5.32909 10.14 5.55309 10.66C5.77709 11.172 6.08109 11.568 6.46509 11.848C6.84909 12.12 7.28909 12.256 7.78509 12.256ZM16.1054 13.144C15.3614 13.144 14.7174 12.964 14.1734 12.604C13.6374 12.236 13.2254 11.728 12.9374 11.08C12.6494 10.424 12.5054 9.664 12.5054 8.8C12.5054 7.936 12.6494 7.18 12.9374 6.532C13.2254 5.876 13.6374 5.368 14.1734 5.008C14.7174 4.64 15.3614 4.456 16.1054 4.456C16.8494 4.456 17.4894 4.64 18.0254 5.008C18.5614 5.368 18.9734 5.876 19.2614 6.532C19.5494 7.18 19.6934 7.936 19.6934 8.8C19.6934 9.664 19.5494 10.424 19.2614 11.08C18.9734 11.728 18.5614 12.236 18.0254 12.604C17.4894 12.964 16.8494 13.144 16.1054 13.144ZM16.1054 12.256C16.5934 12.256 17.0294 12.12 17.4134 11.848C17.8054 11.568 18.1094 11.172 18.3254 10.66C18.5494 10.14 18.6614 9.52 18.6614 8.8C18.6614 8.08 18.5494 7.464 18.3254 6.952C18.1094 6.44 17.8054 6.048 17.4134 5.776C17.0294 5.496 16.5934 5.356 16.1054 5.356C15.6094 5.356 15.1694 5.496 14.7854 5.776C14.4014 6.048 14.0974 6.44 13.8734 6.952C13.6494 7.464 13.5374 8.08 13.5374 8.8C13.5374 9.52 13.6494 10.14 13.8734 10.66C14.0974 11.172 14.4014 11.568 14.7854 11.848C15.1694 12.12 15.6094 12.256 16.1054 12.256Z" fill="#585858"/>
<path d="M0.756 91V90.292C1.324 89.844 1.86 89.392 2.364 88.936C2.876 88.472 3.328 88.016 3.72 87.568C4.12 87.12 4.432 86.68 4.656 86.248C4.888 85.816 5.004 85.4 5.004 85C5.004 84.704 4.952 84.428 4.848 84.172C4.752 83.916 4.592 83.712 4.368 83.56C4.144 83.4 3.84 83.32 3.456 83.32C3.088 83.32 2.78 83.404 2.532 83.572C2.284 83.732 2.096 83.948 1.968 84.22C1.848 84.492 1.788 84.784 1.788 85.096H0.816C0.816 84.544 0.932 84.072 1.164 83.68C1.396 83.28 1.712 82.976 2.112 82.768C2.512 82.56 2.964 82.456 3.468 82.456C4.212 82.456 4.82 82.668 5.292 83.092C5.772 83.508 6.012 84.132 6.012 84.964C6.012 85.46 5.888 85.952 5.64 86.44C5.392 86.92 5.076 87.388 4.692 87.844C4.308 88.292 3.896 88.712 3.456 89.104C3.024 89.496 2.62 89.848 2.244 90.16H6.3V91H0.756ZM10.6112 91.144C10.0352 91.144 9.53519 91.04 9.11119 90.832C8.68719 90.624 8.34719 90.34 8.09119 89.98C7.84319 89.612 7.69119 89.196 7.63519 88.732H8.61919C8.71519 89.196 8.93919 89.572 9.29119 89.86C9.64319 90.14 10.0872 90.28 10.6232 90.28C11.0232 90.28 11.3672 90.188 11.6552 90.004C11.9432 89.812 12.1632 89.556 12.3152 89.236C12.4752 88.916 12.5552 88.56 12.5552 88.168C12.5552 87.544 12.3792 87.04 12.0272 86.656C11.6752 86.272 11.2192 86.08 10.6592 86.08C10.1952 86.08 9.79919 86.184 9.47119 86.392C9.14319 86.6 8.90319 86.872 8.75119 87.208H7.79119L8.51119 82.6H12.9152V83.476H9.29119L8.79919 86.032C8.99119 85.8 9.25519 85.608 9.59119 85.456C9.93519 85.304 10.3272 85.228 10.7672 85.228C11.3192 85.228 11.7992 85.356 12.2072 85.612C12.6232 85.868 12.9472 86.216 13.1792 86.656C13.4112 87.096 13.5272 87.596 13.5272 88.156C13.5272 88.692 13.4112 89.188 13.1792 89.644C12.9552 90.1 12.6232 90.464 12.1832 90.736C11.7512 91.008 11.2272 91.144 10.6112 91.144Z" fill="#585858"/>
<path d="M3.744 65.144C3.168 65.144 2.668 65.04 2.244 64.832C1.82 64.624 1.48 64.34 1.224 63.98C0.976 63.612 0.824 63.196 0.768 62.732H1.752C1.848 63.196 2.072 63.572 2.424 63.86C2.776 64.14 3.22 64.28 3.756 64.28C4.156 64.28 4.5 64.188 4.788 64.004C5.076 63.812 5.296 63.556 5.448 63.236C5.608 62.916 5.688 62.56 5.688 62.168C5.688 61.544 5.512 61.04 5.16 60.656C4.808 60.272 4.352 60.08 3.792 60.08C3.328 60.08 2.932 60.184 2.604 60.392C2.276 60.6 2.036 60.872 1.884 61.208H0.924L1.644 56.6H6.048V57.476H2.424L1.932 60.032C2.124 59.8 2.388 59.608 2.724 59.456C3.068 59.304 3.46 59.228 3.9 59.228C4.452 59.228 4.932 59.356 5.34 59.612C5.756 59.868 6.08 60.216 6.312 60.656C6.544 61.096 6.66 61.596 6.66 62.156C6.66 62.692 6.544 63.188 6.312 63.644C6.088 64.1 5.756 64.464 5.316 64.736C4.884 65.008 4.36 65.144 3.744 65.144ZM11.5117 65.144C10.7677 65.144 10.1237 64.964 9.57966 64.604C9.04366 64.236 8.63166 63.728 8.34366 63.08C8.05566 62.424 7.91166 61.664 7.91166 60.8C7.91166 59.936 8.05566 59.18 8.34366 58.532C8.63166 57.876 9.04366 57.368 9.57966 57.008C10.1237 56.64 10.7677 56.456 11.5117 56.456C12.2557 56.456 12.8957 56.64 13.4317 57.008C13.9677 57.368 14.3797 57.876 14.6677 58.532C14.9557 59.18 15.0997 59.936 15.0997 60.8C15.0997 61.664 14.9557 62.424 14.6677 63.08C14.3797 63.728 13.9677 64.236 13.4317 64.604C12.8957 64.964 12.2557 65.144 11.5117 65.144ZM11.5117 64.256C11.9997 64.256 12.4357 64.12 12.8197 63.848C13.2117 63.568 13.5157 63.172 13.7317 62.66C13.9557 62.14 14.0677 61.52 14.0677 60.8C14.0677 60.08 13.9557 59.464 13.7317 58.952C13.5157 58.44 13.2117 58.048 12.8197 57.776C12.4357 57.496 11.9997 57.356 11.5117 57.356C11.0157 57.356 10.5757 57.496 10.1917 57.776C9.80766 58.048 9.50366 58.44 9.27966 58.952C9.05566 59.464 8.94366 60.08 8.94366 60.8C8.94366 61.52 9.05566 62.14 9.27966 62.66C9.50366 63.172 9.80766 63.568 10.1917 63.848C10.5757 64.12 11.0157 64.256 11.5117 64.256Z" fill="#585858"/>
<path d="M1.584 39L4.92 31.464H0.312V30.6H5.952V31.356L2.652 39H1.584ZM9.79088 39.144C9.21488 39.144 8.71488 39.04 8.29088 38.832C7.86688 38.624 7.52688 38.34 7.27088 37.98C7.02288 37.612 6.87088 37.196 6.81488 36.732H7.79888C7.89488 37.196 8.11888 37.572 8.47088 37.86C8.82288 38.14 9.26688 38.28 9.80288 38.28C10.2029 38.28 10.5469 38.188 10.8349 38.004C11.1229 37.812 11.3429 37.556 11.4949 37.236C11.6549 36.916 11.7349 36.56 11.7349 36.168C11.7349 35.544 11.5589 35.04 11.2069 34.656C10.8549 34.272 10.3989 34.08 9.83888 34.08C9.37488 34.08 8.97888 34.184 8.65088 34.392C8.32288 34.6 8.08288 34.872 7.93088 35.208H6.97088L7.69088 30.6H12.0949V31.476H8.47088L7.97888 34.032C8.17088 33.8 8.43488 33.608 8.77088 33.456C9.11488 33.304 9.50688 33.228 9.94688 33.228C10.4989 33.228 10.9789 33.356 11.3869 33.612C11.8029 33.868 12.1269 34.216 12.3589 34.656C12.5909 35.096 12.7069 35.596 12.7069 36.156C12.7069 36.692 12.5909 37.188 12.3589 37.644C12.1349 38.1 11.8029 38.464 11.3629 38.736C10.9309 39.008 10.4069 39.144 9.79088 39.144Z" fill="#585858"/>
<path d="M4.164 117.144C3.42 117.144 2.776 116.964 2.232 116.604C1.696 116.236 1.284 115.728 0.996 115.08C0.708 114.424 0.564 113.664 0.564 112.8C0.564 111.936 0.708 111.18 0.996 110.532C1.284 109.876 1.696 109.368 2.232 109.008C2.776 108.64 3.42 108.456 4.164 108.456C4.908 108.456 5.548 108.64 6.084 109.008C6.62 109.368 7.032 109.876 7.32 110.532C7.608 111.18 7.752 111.936 7.752 112.8C7.752 113.664 7.608 114.424 7.32 115.08C7.032 115.728 6.62 116.236 6.084 116.604C5.548 116.964 4.908 117.144 4.164 117.144ZM4.164 116.256C4.652 116.256 5.088 116.12 5.472 115.848C5.864 115.568 6.168 115.172 6.384 114.66C6.608 114.14 6.72 113.52 6.72 112.8C6.72 112.08 6.608 111.464 6.384 110.952C6.168 110.44 5.864 110.048 5.472 109.776C5.088 109.496 4.652 109.356 4.164 109.356C3.668 109.356 3.228 109.496 2.844 109.776C2.46 110.048 2.156 110.44 1.932 110.952C1.708 111.464 1.596 112.08 1.596 112.8C1.596 113.52 1.708 114.14 1.932 114.66C2.156 115.172 2.46 115.568 2.844 115.848C3.228 116.12 3.668 116.256 4.164 116.256Z" fill="#585858"/>
<defs>
<filter id="filter0_dd_1203_27349" x="46" y="69" width="68" height="69" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1203_27349"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1203_27349" result="effect2_dropShadow_1203_27349"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1203_27349" result="shape"/>
</filter>
<filter id="filter1_dd_1203_27349" x="106" y="34" width="68" height="104" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1203_27349"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1203_27349" result="effect2_dropShadow_1203_27349"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1203_27349" result="shape"/>
</filter>
<filter id="filter2_dd_1203_27349" x="166" y="48" width="68" height="90" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1203_27349"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1203_27349" result="effect2_dropShadow_1203_27349"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1203_27349" result="shape"/>
</filter>
<filter id="filter3_dd_1203_27349" x="226" y="56" width="68" height="82" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1203_27349"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1203_27349" result="effect2_dropShadow_1203_27349"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1203_27349" result="shape"/>
</filter>
<filter id="filter4_dd_1203_27349" x="286" y="24" width="68" height="114" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1203_27349"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1203_27349" result="effect2_dropShadow_1203_27349"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1203_27349" result="shape"/>
</filter>
<filter id="filter5_dd_1203_27349" x="346" y="34" width="68" height="104" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1203_27349"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1203_27349" result="effect2_dropShadow_1203_27349"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1203_27349" result="shape"/>
</filter>
<filter id="filter6_dd_1203_27349" x="406" y="71" width="68" height="67" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1203_27349"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1203_27349" result="effect2_dropShadow_1203_27349"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1203_27349" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1203_27349" x1="20" y1="-20.8796" x2="20" y2="38.7222" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_1203_27349" x1="80" y1="58.1204" x2="80" y2="125.315" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_1203_27349" x1="20" y1="-38.7037" x2="20" y2="71.7778" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_1203_27349" x1="140" y1="5.29629" x2="140" y2="129.852" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_1203_27349" x1="20" y1="-31.5741" x2="20" y2="58.5556" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_linear_1203_27349" x1="200" y1="26.4259" x2="200" y2="128.037" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint6_linear_1203_27349" x1="20" y1="-27.5" x2="20" y2="51" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_1203_27349" x1="260" y1="38.5" x2="260" y2="127" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint8_linear_1203_27349" x1="20" y1="-43.7963" x2="20" y2="81.2222" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint9_linear_1203_27349" x1="320" y1="-9.7963" x2="320" y2="131.148" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint10_linear_1203_27349" x1="20" y1="-38.7037" x2="20" y2="71.7778" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint11_linear_1203_27349" x1="380" y1="5.29629" x2="380" y2="129.852" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint12_linear_1203_27349" x1="20" y1="-19.8611" x2="20" y2="36.8333" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint13_linear_1203_27349" x1="440" y1="61.1389" x2="440" y2="125.056" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#3BEE5C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint14_linear_1203_27349" x1="240" y1="0" x2="240" y2="11.493" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0.02"/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint15_linear_1203_27349" x1="240" y1="0" x2="240" y2="11.493" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0.02"/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_1203_27349">
<rect x="42" width="438" height="120" rx="8" fill="white"/>
</clipPath>
</defs>
</svg>
