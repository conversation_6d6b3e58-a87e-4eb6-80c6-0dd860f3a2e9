<svg width="164" height="140" viewBox="0 0 164 140" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="7" width="140" height="140" rx="70" fill="#DDDDDD"/>
<rect x="7" width="140" height="140" rx="70" fill="url(#paint0_linear_1203_39726)"/>
<g filter="url(#filter0_d_1203_39726)">
<g clip-path="url(#clip0_1203_39726)">
<rect x="12.2031" y="49.8398" width="129.6" height="79.2" rx="3.90857" fill="white"/>
<circle cx="19.048" cy="56.6804" r="1.95429" fill="#005B93"/>
<circle cx="24.9074" cy="56.6804" r="1.95429" fill="#CCCCCC"/>
<circle cx="30.7746" cy="56.6804" r="1.95429" fill="#23A6F9"/>
<line x1="12.2031" y1="63.2762" x2="149.003" y2="63.2762" stroke="#B2C4D7" stroke-opacity="0.3" stroke-width="0.488571"/>
</g>
<rect x="12.5631" y="50.1998" width="128.88" height="78.48" rx="3.54857" stroke="#DEDEDE" stroke-width="0.72"/>
</g>
<g filter="url(#filter1_d_1203_39726)">
<rect x="5" y="13.8398" width="144" height="108" rx="4.85393" fill="white"/>
<rect x="5.36" y="14.1998" width="143.28" height="107.28" rx="4.49393" stroke="#DEDEDE" stroke-width="0.72"/>
<line x1="5" y1="32.2001" x2="149" y2="32.2001" stroke="#DEDEDE" stroke-opacity="0.3" stroke-width="0.72"/>
<rect x="12.2031" y="21.04" width="28.8" height="4.32" rx="2.16" fill="#DEDEDE"/>
<rect x="122" y="21.04" width="20.16" height="4.32" rx="2.16" fill="#DEDEDE"/>
<rect x="53.7816" y="39.9398" width="88.2" height="14.04" rx="4.14" stroke="#DEDEDE" stroke-width="0.36"/>
<rect x="60.7969" y="44.8003" width="17.28" height="4.32" rx="2.16" fill="#ACACAC"/>
<rect x="127.758" y="44.8003" width="10.08" height="4.32" rx="2.16" fill="#ACACAC"/>
<rect x="127.758" y="44.8003" width="10.08" height="4.32" rx="2.16" fill="url(#paint1_radial_1203_39726)"/>
<rect x="116.953" y="44.8003" width="10.08" height="4.32" rx="2.16" fill="#DEDEDE"/>
<rect x="79.5156" y="44.8003" width="33.12" height="4.32" rx="2.16" fill="#ACACAC"/>
<rect x="53.7816" y="57.2198" width="88.2" height="14.04" rx="4.14" stroke="#DEDEDE" stroke-width="0.36"/>
<rect x="60.7969" y="62.0798" width="17.28" height="4.32" rx="2.16" fill="#ACACAC"/>
<rect x="127.758" y="62.0798" width="10.08" height="4.32" rx="2.16" fill="#ACACAC"/>
<rect x="127.758" y="62.0798" width="10.08" height="4.32" rx="2.16" fill="url(#paint2_radial_1203_39726)"/>
<rect x="116.953" y="62.0798" width="10.08" height="4.32" rx="2.16" fill="#DEDEDE"/>
<rect x="79.5156" y="62.0798" width="33.12" height="4.32" rx="2.16" fill="#ACACAC"/>
<rect x="53.7816" y="74.4998" width="88.2" height="14.04" rx="4.14" stroke="#DEDEDE" stroke-width="0.36"/>
<rect x="60.7969" y="79.3604" width="17.28" height="4.32" rx="2.16" fill="#ACACAC"/>
<rect x="127.758" y="79.3604" width="10.08" height="4.32" rx="2.16" fill="#ACACAC"/>
<rect x="127.758" y="79.3604" width="10.08" height="4.32" rx="2.16" fill="url(#paint3_radial_1203_39726)"/>
<rect x="116.953" y="79.3604" width="10.08" height="4.32" rx="2.16" fill="#DEDEDE"/>
<rect x="79.5156" y="79.3604" width="33.12" height="4.32" rx="2.16" fill="#ACACAC"/>
<rect x="53.7816" y="91.7797" width="88.2" height="14.04" rx="4.14" stroke="#DEDEDE" stroke-width="0.36"/>
<rect x="60.7969" y="96.6393" width="17.28" height="4.32" rx="2.16" fill="#ACACAC"/>
<rect x="127.758" y="96.6393" width="10.08" height="4.32" rx="2.16" fill="#ACACAC"/>
<rect x="127.758" y="96.6393" width="10.08" height="4.32" rx="2.16" fill="url(#paint4_radial_1203_39726)"/>
<rect x="116.953" y="96.6393" width="10.08" height="4.32" rx="2.16" fill="#DEDEDE"/>
<rect x="79.5156" y="96.6393" width="33.12" height="4.32" rx="2.16" fill="#ACACAC"/>
<rect x="12.2031" y="39.7598" width="33.12" height="4.32" rx="2.16" fill="#DEDEDE"/>
<rect x="12.2031" y="46.96" width="33.12" height="4.32" rx="2.16" fill="#DEDEDE"/>
<rect x="12.5625" y="54.1602" width="20.16" height="4.32" rx="2.16" fill="#DEDEDE"/>
</g>
<g filter="url(#filter2_d_1203_39726)">
<g filter="url(#filter3_d_1203_39726)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M157.188 102.172L154.218 105L145.307 96.5155L148.278 93.6855L157.188 102.172ZM131.987 92.9995C125.041 92.9995 119.388 87.6175 119.388 80.9995C119.388 74.3835 125.041 68.9995 131.987 68.9995C138.933 68.9995 144.588 74.3835 144.588 80.9995C144.588 87.6175 138.933 92.9995 131.987 92.9995ZM131.987 64.9995C122.709 64.9995 115.188 72.1635 115.188 80.9995C115.188 89.8355 122.709 96.9995 131.987 96.9995C141.265 96.9995 148.787 89.8355 148.787 80.9995C148.787 72.1635 141.265 64.9995 131.987 64.9995Z" fill="#21543E"/>
</g>
</g>
<defs>
<filter id="filter0_d_1203_39726" x="7.31741" y="48.8627" width="139.373" height="88.9714" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.90857"/>
<feGaussianBlur stdDeviation="2.44286"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1203_39726"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1203_39726" result="shape"/>
</filter>
<filter id="filter1_d_1203_39726" x="0.114285" y="12.8627" width="153.771" height="117.771" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.90857"/>
<feGaussianBlur stdDeviation="2.44286"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1203_39726"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1203_39726" result="shape"/>
</filter>
<filter id="filter2_d_1203_39726" x="108.775" y="63.717" width="56.1375" height="52.8252" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5.13"/>
<feGaussianBlur stdDeviation="3.20625"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1203_39726"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1203_39726" result="shape"/>
</filter>
<filter id="filter3_d_1203_39726" x="108.775" y="60.587" width="54.825" height="52.825" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3.20625"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1203_39726"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1203_39726" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1203_39726" x1="82.2014" y1="0" x2="82.2014" y2="140" gradientUnits="userSpaceOnUse">
<stop stop-color="#20523C"/>
<stop offset="1" stop-color="#2E7E5C"/>
</linearGradient>
<radialGradient id="paint1_radial_1203_39726" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(132.833 49.8246) rotate(-90.4865) scale(4.09403 6.63676)">
<stop stop-color="#C3F63C"/>
<stop offset="1" stop-color="#54D10E"/>
</radialGradient>
<radialGradient id="paint2_radial_1203_39726" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(132.833 67.1042) rotate(-90.4865) scale(4.09403 6.63676)">
<stop stop-color="#C3F63C"/>
<stop offset="1" stop-color="#54D10E"/>
</radialGradient>
<radialGradient id="paint3_radial_1203_39726" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(132.833 84.3847) rotate(-90.4865) scale(4.09403 6.63676)">
<stop stop-color="#C3F63C"/>
<stop offset="1" stop-color="#54D10E"/>
</radialGradient>
<radialGradient id="paint4_radial_1203_39726" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(132.833 101.664) rotate(-90.4865) scale(4.09403 6.63676)">
<stop stop-color="#C3F63C"/>
<stop offset="1" stop-color="#54D10E"/>
</radialGradient>
<clipPath id="clip0_1203_39726">
<rect x="12.2031" y="49.8398" width="129.6" height="79.2" rx="3.90857" fill="white"/>
</clipPath>
</defs>
</svg>
