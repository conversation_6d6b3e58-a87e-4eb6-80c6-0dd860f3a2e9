<svg width="140" height="140" viewBox="0 0 140 140" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="140" height="140" rx="70" fill="#DDDDDD"/>
<rect width="140" height="140" rx="70" fill="url(#paint0_linear_1249_37593)"/>
<g clip-path="url(#clip0_1249_37593)">
<g filter="url(#filter0_d_1249_37593)">
<path d="M96.901 49.1074L59.3177 86.6908L42.2344 69.6074" stroke="white" stroke-width="10" stroke-linecap="square" shape-rendering="crispEdges"/>
</g>
</g>
<rect x="5.3" y="5.3" width="129.4" height="129.4" rx="64.7" stroke="#DDDDDD" stroke-width="0.6"/>
<defs>
<filter id="filter0_d_1249_37593" x="15.1641" y="32.0361" width="108.805" height="91.7256" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1249_37593"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1249_37593" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1249_37593" x1="75.2014" y1="0" x2="75.2014" y2="140" gradientUnits="userSpaceOnUse">
<stop stop-color="#20523C"/>
<stop offset="1" stop-color="#2E7E5C"/>
</linearGradient>
<clipPath id="clip0_1249_37593">
<rect width="82" height="82" fill="white" transform="translate(28.5625 28.6074)"/>
</clipPath>
</defs>
</svg>
