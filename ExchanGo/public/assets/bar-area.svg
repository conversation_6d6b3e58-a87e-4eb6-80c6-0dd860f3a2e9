<svg width="744" height="144" viewBox="0 0 744 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1079_36235)">
<rect width="744" height="144" rx="8" fill="white"/>
<line x1="53.5057" y1="174" x2="53.5057" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="107.506" y1="174" x2="107.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="161.506" y1="174" x2="161.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="215.506" y1="174" x2="215.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="269.506" y1="174" x2="269.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="323.506" y1="174" x2="323.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="377.506" y1="174" x2="377.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="431.506" y1="174" x2="431.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="485.506" y1="174" x2="485.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="539.506" y1="174" x2="539.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="593.506" y1="174" x2="593.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="647.506" y1="174" x2="647.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line x1="701.506" y1="174" x2="701.506" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line y1="28.662" x2="744" y2="28.662" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line y1="57.662" x2="744" y2="57.662" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line y1="86.662" x2="744" y2="86.662" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<line y1="115.662" x2="744" y2="115.662" stroke="#DEDEDE" stroke-width="0.676056" stroke-dasharray="1.35 1.35"/>
<mask id="mask0_1079_36235" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="-5" y="26" width="754" height="141">
<path d="M54 116.5H-3.5V165.5H748V94L689.5 75H583.5L486.5 106L378.5 27.5L270 75H161L54 116.5Z" fill="#D9D9D9" stroke="#3BEE5C" stroke-width="2"/>
</mask>
<g mask="url(#mask0_1079_36235)">
<g opacity="0.2" filter="url(#filter0_f_1079_36235)">
<path d="M531 17.9204C531 55.4317 462.276 85.8407 377.5 85.8407C292.724 85.8407 224 55.4317 224 17.9204C224 -19.591 292.724 -50 377.5 -50C462.276 -50 531 -19.591 531 17.9204Z" fill="url(#paint0_linear_1079_36235)"/>
</g>
<g filter="url(#filter1_dd_1079_36235)">
<path d="M-3.5 116.5H54L161 75H270L378.5 27.5L486.5 106L583.5 75H689.5L748 94" stroke="#3BEE5C" stroke-width="2"/>
</g>
</g>
<path d="M-3.5 116.5H54L161 75H270L378.5 27.5L486.5 106L583.5 75H689.5L748 94" stroke="#3BEE5C" stroke-width="2"/>
<rect width="744" height="11" fill="url(#paint1_linear_1079_36235)"/>
<rect width="744" height="11" transform="matrix(1 0 0 -1 0 144)" fill="url(#paint2_linear_1079_36235)"/>
<g filter="url(#filter2_d_1079_36235)">
<circle cx="377.5" cy="27" r="6" fill="#3BEE5C"/>
<circle cx="377.5" cy="27" r="5" stroke="white" stroke-width="2"/>
</g>
</g>
<rect x="0.25" y="0.25" width="743.5" height="143.5" rx="7.75" stroke="#DEDEDE" stroke-width="0.5"/>
<defs>
<filter id="filter0_f_1079_36235" x="88.1593" y="-185.841" width="578.681" height="407.522" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="67.9204" result="effect1_foregroundBlur_1079_36235"/>
</filter>
<filter id="filter1_dd_1079_36235" x="-27.5" y="10.354" width="799.812" height="139.146" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0461122 0 0 0 0 0.639423 0 0 0 0 0.155494 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1079_36235"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0461122 0 0 0 0 0.639423 0 0 0 0 0.155494 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1079_36235" result="effect2_dropShadow_1079_36235"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1079_36235" result="shape"/>
</filter>
<filter id="filter2_d_1079_36235" x="368.5" y="21" width="18" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1079_36235"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1079_36235" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1079_36235" x1="366.755" y1="85.8407" x2="368.868" y2="22.3873" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#0EB12C"/>
</linearGradient>
<linearGradient id="paint1_linear_1079_36235" x1="372" y1="0" x2="372" y2="11" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0.02"/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_1079_36235" x1="372" y1="0" x2="372" y2="11" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0.02"/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_1079_36235">
<rect width="744" height="144" rx="8" fill="white"/>
</clipPath>
</defs>
</svg>
