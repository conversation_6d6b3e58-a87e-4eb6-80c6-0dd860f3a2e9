import React, { useState } from "react";
import ToggleButton from "../ui/ToggleButton";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import GradientButton from "../ui/GradientButton";

const NotificationsReminders = () => {
  const [toggleChecked, setToggleChecked] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleToggleChange = (checked: boolean) => {
    setToggleChecked(checked);
    setIsDirty(true);
  };

  const handleSave = () => {
    setIsSaving(true);
    // Simulate save logic (replace with real API call if needed)
    setTimeout(() => {
      setIsSaving(false);
      setIsDirty(false);
      toast.success("Changes saved successfully.", {
        position: "top-right",
        autoClose: 2500,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    }, 800);
  };

  return (
    <div>
      <div className="flex items-start gap-6 justify-between mb-8">
        <div>
          <h2 className="text-[16px] font-semibold leading-[24px]">
            Update Rate
          </h2>
          <p className="text-[#424242] text-[14px] leading-[21px] font-normal max-w-[295px] mt-1">
            Notify me (via WhatsApp or Email) if exchange rates haven't been
            updated for 24h
          </p>
        </div>
        <ToggleButton
          checked={toggleChecked}
          size="md"
          onChange={handleToggleChange}
          className="w-[37px]"
        />
      </div>
      <div className="bg-[#DEDEDE] w-full h-[1px] mb-6"></div>
      <div className="flex items-end justify-end w-full">
        <GradientButton
          className="h-[46px]"
          disabled={!isDirty || isSaving}
          onClick={handleSave}
        >
          {" "}
          {isSaving ? "Saving..." : "Save Update"}
        </GradientButton>
      </div>
    </div>
  );
};

export default NotificationsReminders;
