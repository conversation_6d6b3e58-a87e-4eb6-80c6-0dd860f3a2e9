"use client"
import React, { useState } from 'react';
import Checkbox from '../ui/Checkbox';

const CURRENCIES = [
    'USD', 'CHF', 'AUD', 'CAD', 'GBP', 'CHF', 'SAR', 'QAR', 'KWD', 'CNY', 'TRY', 'JPY', 'NOK', 'KRW', 'RUB', 'INR',
];

const TREND_FILTERS = [
    { label: 'Popular Exchange', icon: '🔥' },
    { label: 'Most Searched', icon: '⚡' },
    { label: 'Nearest office', icon: '', },
];

const FilterSpesific: React.FC = () => {
    const [isOpenOffices, setIsOpenOffices] = useState<boolean>(false);
    const [showAll, setShowAll] = useState<boolean>(false);
    const [showOpenOnly, setShowOpenOnly] = useState<boolean>(false);
    const [selectedCurrencies, setSelectedCurrencies] = useState<number[]>([]);
    const [selectedTrend, setSelectedTrend] = useState<number | null>(null);

    const visibleCurrencies = showAll ? CURRENCIES : CURRENCIES.slice(0, 10);

    const handleCurrencyClick = (idx: number) => {
        setSelectedCurrencies((prev) =>
            prev.includes(idx) ? prev.filter((i) => i !== idx) : [...prev, idx]
        );
    };

    const handleTrendClick = (idx: number) => {
        setSelectedTrend((prev) => (prev === idx ? null : idx));
    };

    const handleClearAll = () => {
        setSelectedCurrencies([]);
        setSelectedTrend(null);
        setShowOpenOnly(false);
    };

    const handleCheckboxChange = (checked: boolean) => {
        setIsOpenOffices(checked);
    };
    return (
        <div className="w-[200px] xl:w-[292px]">
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-[#111111] text-[20px] leading-[24px] font-bold">Filter</h2>
                <button
                    className="text-[#20523C] text-[14px] leading-[20px] font-bold transition duration-200 hover:underline cursor-pointer"
                    onClick={handleClearAll}
                >
                    Clear All
                </button>
            </div>

            {/* By Available Currencies */}
            <div className="mb-8">
                <h3 className="text-[#111111] text-[16px] leading-[16px] font-bold mb-4">By Available Currencies</h3>
                <div className="flex items-center flex-wrap gap-1.5">
                    {visibleCurrencies.map((cur, idx) => (
                        <button
                            key={`${cur}-${idx}`}
                            className={`px-3 py-2 cursor-pointer rounded-full border text-[14px] leading-[17px] font-normal transition
                ${selectedCurrencies.includes(idx)
                                    ? 'bg-[#20523C] border-[#20523C] text-white'
                                    : 'text-[#111111] border-[#DEDEDE]'}
              `}
                            onClick={() => handleCurrencyClick(idx)}
                            type="button"
                        >
                            {cur}
                        </button>
                    ))}
                    {CURRENCIES.length > 15 && (
                        <button
                            className="px-3 py-2 rounded-full cursor-pointer border border-[#DEDEDE] bg-[#F1F1F1] text-[14px] leading-[17px] font-normal text-[#585858] hover:bg-gray-200"
                            onClick={() => setShowAll((s) => !s)}
                            type="button"
                        >
                            {showAll ? 'Showless' : 'Showmore'}
                        </button>
                    )}
                </div>
            </div>

            {/* By Trend */}
            <div className="mb-8">
                <h3 className="text-[#111111] text-[16px] leading-[16px] font-bold mb-4">By Trend</h3>
                <div className="flex flex-col gap-1.5">
                    {TREND_FILTERS.map((trend, idx) => (
                        <button
                            key={trend.label}
                            className={`w-fit cursor-pointer flex items-center px-3 py-2 rounded-full border text-[14px] leading-[17px] font-normal transition
                ${selectedTrend === idx
                                    ? `bg-[#20523C] border-[#20523C] text-white`
                                    : `border-[#DEDEDE] text-[#111111]`}
              `}
                            onClick={() => handleTrendClick(idx)}
                            type="button"
                        >
                            {trend.icon && <span role="img" aria-label={trend.label}>{trend.icon}</span>}
                            {trend.label}
                        </button>
                    ))}
                </div>
            </div>

            {/* By Office Hour */}
            <div>
                <h3 className="text-[#111111] text-[16px] leading-[16px] font-bold mb-4">By Office Hour</h3>
                <div className="flex items-center gap-2.5">
                    <Checkbox
                        checked={isOpenOffices}
                        onChange={handleCheckboxChange}
                    />
                    <h3 className="text-[#111111] text-[16px] leading-[16px] font-normal text-left">
                        Show only currently open offices
                    </h3>
                </div>
            </div>
        </div>
    );
};

export default FilterSpesific;