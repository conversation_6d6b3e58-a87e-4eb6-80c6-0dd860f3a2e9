import React from 'react'

const ExchangeLeaderboard = () => {
     return (
          <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
               <path d="M22.3076 15.2073C22.3076 19.1307 19.1344 22.304 15.2109 22.304L16.2754 20.5298" stroke="#292D32" strokeWidth="1.52072" strokeLinecap="round" strokeLinejoin="round" />
               <path d="M2.03125 9.12426C2.03125 5.20082 5.20448 2.02759 9.12793 2.02759L8.06344 3.80176" stroke="#292D32" strokeWidth="1.52072" strokeLinecap="round" strokeLinejoin="round" />
               <circle cx="16.2253" cy="5.06905" r="4.3087" fill="white" stroke="#292D32" strokeWidth="1.52072" />
               <circle cx="19.2644" cy="9.12423" r="4.3087" fill="white" stroke="#292D32" strokeWidth="1.52072" />
               <circle cx="19.2644" cy="5.06905" r="4.3087" fill="white" stroke="#292D32" strokeWidth="1.52072" />
               <circle cx="7.09668" cy="17.2349" r="6.33632" stroke="#292D32" strokeWidth="1.52072" />
               <path d="M5.07812 18.6511C5.07812 19.4363 5.68521 20.0676 6.42988 20.0676H7.95159C8.59913 20.0676 9.12527 19.5172 9.12527 18.8292C9.12527 18.0926 8.8015 17.8255 8.32394 17.6555L5.88755 16.8056C5.40999 16.6357 5.08623 16.3767 5.08623 15.632C5.08623 14.9521 5.61234 14.3936 6.25988 14.3936H7.78161C8.52629 14.3936 9.13337 15.0249 9.13337 15.8101" stroke="#292D32" strokeWidth="1.35175" strokeLinecap="round" strokeLinejoin="round" />
               <path d="M7.10156 13.5923V20.8771" stroke="#292D32" strokeWidth="1.35175" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
     )
}

export default ExchangeLeaderboard