"use client";

import type React from "react";
import { useRef, useEffect, useState } from "react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import { exchangeOffices } from "../data";
import Image from "next/image";
import ReactDOM from "react-dom/client";
import CustomMapMarker from "./CustomMapMarker";
import CustomMapPopup from "./CustomMapPopup";
import type { ExchangeOffice } from "../types";
import ExchangeCard from "../ExchangeCard";
import { useHover, HoverProvider } from "@/context/HoverContext";

mapboxgl.accessToken =
  "pk.eyJ1IjoiZXhjaGFuZ28yNCIsImEiOiJjbWJobzNtbXkwYzd2MmtzZ3M0Nmlhem1wIn0.WWU3U5Ur4wsdKokNEk1DZQ";

interface LocationSuggestion {
  id: string;
  name: string;
  country: string;
  coordinates: {
    lng: number;
    lat: number;
  };
}

interface MapViewProps {
  filteredOffices?: ExchangeOffice[];
}

// Add predefined Moroccan cities
const predefinedLocations: LocationSuggestion[] = [
  {
    id: "1",
    name: "Casablanca",
    country: "Maroc",
    coordinates: { lng: -7.5898, lat: 33.5731 },
  },
  {
    id: "2",
    name: "Rabat",
    country: "Maroc",
    coordinates: { lng: -6.8498, lat: 34.0209 },
  },
  {
    id: "3",
    name: "Fès",
    country: "Maroc",
    coordinates: { lng: -5.0078, lat: 34.0333 },
  },
  {
    id: "4",
    name: "Tanger",
    country: "Maroc",
    coordinates: { lng: -5.8129, lat: 35.7595 },
  },
  {
    id: "5",
    name: "Agadir",
    country: "Maroc",
    coordinates: { lng: -9.5982, lat: 30.4278 },
  },
  {
    id: "6",
    name: "Marrakech",
    country: "Maroc",
    coordinates: { lng: -8.0083, lat: 31.6295 },
  },
];

// Add this type definition near the top of the file, after other type definitions
interface WorkingHoursObject {
  fromTime: string;
  toTime: string;
}

// Add this function before the MapView component definition
const renderWorkingHours = (
  hours: string | WorkingHoursObject | undefined
): string => {
  if (!hours) return "";
  if (typeof hours === "string") return hours;
  return `${hours.fromTime} - ${hours.toTime}`;
};

const MapView: React.FC<MapViewProps> = ({
  filteredOffices = exchangeOffices,
}) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const markers = useRef<mapboxgl.Marker[]>([]);
  const popups = useRef<mapboxgl.Popup[]>([]);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [mapError, setMapError] = useState<string | null>(null);
  const userLocationMarker = useRef<mapboxgl.Marker | null>(null);
  const [selectedExchangeId, setSelectedExchangeId] = useState<number | null>(
    null
  );
  const { hoveredExchangeId } = useHover();
  const [hoveredExchangeIdInternal, setHoveredExchangeIdInternal] = useState<
    number | null
  >(null);
  const markerElements = useRef<{ [key: number]: HTMLElement }>({});
  const [initialLocation, setInitialLocation] = useState<{
    lng: number;
    lat: number;
    zoom: number;
  }>({
    lng: -7.6, // Center of Casablanca
    lat: 33.58, // Center of Casablanca
    zoom: 12, // Zoom level to show the city
  });
  const [showSearch, setShowSearch] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchContainerRef = useRef<HTMLDivElement>(null);
  const [suggestions, setSuggestions] = useState<LocationSuggestion[]>([]);
  const [debounceTimeout, setDebounceTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const [isCustomFullscreen, setIsCustomFullscreen] = useState(false);
  const [hoverCardPosition, setHoverCardPosition] = useState<{
    top: number;
    left: number;
  } | null>(null);
  const [hoveredOffice, setHoveredOffice] = useState<ExchangeOffice | null>(
    null
  );
  const [isTouchDevice, setIsTouchDevice] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);

  useEffect(() => {
    const isTouchEnabled =
      "ontouchstart" in window || navigator.maxTouchPoints > 0;
    setIsTouchDevice(isTouchEnabled);

    if (isTouchEnabled) {
      document.body.classList.add("is-touch-device");
    }

    return () => {
      document.body.classList.remove("is-touch-device");
    };
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target as Node) &&
        showSearch
      ) {
        setShowSearch(false);
        setSuggestions([]);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showSearch]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search);
      const locationParam = params.get("location");

      if (locationParam) {
        geocodeLocationName(locationParam);
      }
    }
  }, []);

  const geocodeLocationName = async (locationName: string) => {
    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
          locationName
        )}.json?access_token=${mapboxgl.accessToken}&types=place&country=MA` // Added &country=MA
      );

      if (!response.ok) {
        throw new Error(`Failed to geocode location: ${response.status}`);
      }

      const data = await response.json();

      if (data.features && data.features.length > 0) {
        const [lng, lat] = data.features[0].center;

        setInitialLocation({
          lng,
          lat,
          zoom: 13,
        });

        if (map.current && mapLoaded) {
          map.current.flyTo({
            center: [lng, lat],
            zoom: 13,
            speed: 1.5,
          });

          addUserLocationMarker(lat, lng, locationName);
        }
      }
    } catch (error) {
      console.error("Error geocoding location from URL:", error);
    }
  };

  const addUserLocationMarker = (lat: number, lng: number, name: string) => {
    if (!map.current) return;

    if (userLocationMarker.current) {
      userLocationMarker.current.remove();
      userLocationMarker.current = null;
    }

    const el = document.createElement("div");
    el.className = "user-location-marker";
    el.style.width = "24px";
    el.style.height = "24px";
    el.style.borderRadius = "50%";
    el.style.backgroundColor = "#4B97F8";
    el.style.display = "flex";
    el.style.alignItems = "center";
    el.style.justifyContent = "center";
    el.style.boxShadow = "0 0 0 6px rgba(75, 151, 248, 0.2)";
    el.style.border = "2px solid white";
    el.innerHTML = `<svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3"><circle cx="12" cy="12" r="6"></circle></svg>`;

    const marker = new mapboxgl.Marker(el)
      .setLngLat([lng, lat])
      .addTo(map.current);

    userLocationMarker.current = marker;
  };
  // Targeted function to remove ONLY Morocco-Western Sahara boundary
  const hideWesternSaharaBoundary = (mapInstance: mapboxgl.Map) => {
    if (!mapInstance || !mapInstance.getStyle()?.layers) return;

    const layers = mapInstance.getStyle().layers;

    layers.forEach((layer) => {
      if (!layer.id || typeof layer.id !== "string") return;

      const layerId = layer.id.toLowerCase();

      // Target ONLY Western Sahara specific layers - very specific patterns
      const westernSaharaPatterns = [
        "western-sahara",
        "western_sahara",
        "sahara-boundary",
        "sahara_boundary",
      ];

      // Check for exact Western Sahara matches
      if (westernSaharaPatterns.some((pattern) => layerId.includes(pattern))) {
        try {
          mapInstance.setLayoutProperty(layer.id, "visibility", "none");
          console.log(`Hidden Western Sahara layer: ${layer.id}`);
        } catch (error) {
          console.warn(`Could not hide layer ${layer.id}:`, error);
        }
        return;
      }

      // Target only disputed boundary layers (not all admin boundaries)
      const disputedOnlyPatterns = [
        "admin-0-boundary-disputed",
        "boundary-disputed",
        "disputed-boundary",
        "boundary_disputed",
        "disputed_boundary",
      ];

      if (disputedOnlyPatterns.some((pattern) => layerId === pattern)) {
        try {
          mapInstance.setLayoutProperty(layer.id, "visibility", "none");
          console.log(`Hidden disputed boundary layer: ${layer.id}`);
        } catch (error) {
          console.warn(`Could not hide layer ${layer.id}:`, error);
        }
      }
    });
  };

  // Replace your existing map initialization useEffect with this:
  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    try {
      const mapInstance = new mapboxgl.Map({
        container: mapContainer.current,
        style: "mapbox://styles/mapbox/light-v11",
        center: [initialLocation.lng, initialLocation.lat],
        zoom: initialLocation.zoom,
        attributionControl: false,
        projection: "mercator",
        transformRequest: (url, resourceType) => {
          console.log(`Loading ${resourceType}: ${url}`);
          return { url };
        },
      });

      mapInstance.on("load", () => {
        console.log("Map loaded successfully");
        map.current = mapInstance;
        setMapLoaded(true);

        // Customize colors
        // mapInstance.setPaintProperty("water", "fill-color", "#7CB9E8");
        // mapInstance.setPaintProperty("land", "fill-color", "#F5F5F5");

        // Set map language to French
        if (mapInstance.getStyle().layers) {
          const layers = mapInstance.getStyle().layers;
          layers.forEach((layer) => {
            if (
              layer.type === "symbol" &&
              layer.layout &&
              layer.layout["text-field"]
            ) {
              mapInstance.setLayoutProperty(layer.id, "text-field", [
                "get",
                `name_fr`,
              ]);
            }
          });
        }

        // TARGETED: Hide only Morocco-Western Sahara boundary
        hideWesternSaharaBoundary(mapInstance);

        if (
          initialLocation.lng !== -74.0066 ||
          initialLocation.lat !== 40.7135
        ) {
          addUserLocationMarker(
            initialLocation.lat,
            initialLocation.lng,
            "Selected Location"
          );
        }

        // Add click handler to close hover card when clicking on the map (not on a marker)
        mapInstance.on("click", () => {
          // Only close if the click is directly on the map (not bubbled from a marker)
          if (selectedExchangeId) {
            setSelectedExchangeId(null);
            setHoveredExchangeIdInternal(null);
            setHoveredOffice(null);
            setHoverCardPosition(null);
          }
        });
      });

      // Re-check when style updates (but only for Western Sahara)
      mapInstance.on("styledata", () => {
        setTimeout(() => {
          hideWesternSaharaBoundary(mapInstance);
        }, 100);
      });

      mapInstance.on("error", (e) => {
        const errorMessage = e.error
          ? e.error.message || JSON.stringify(e.error)
          : "Unknown map error";
        console.error("Mapbox error:", errorMessage);
        setMapError(errorMessage);
      });

      const nav = new mapboxgl.NavigationControl({
        showCompass: false,
        visualizePitch: false,
      });
      mapInstance.addControl(nav, "bottom-right");
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Unknown error initializing map";
      console.error("Error initializing map:", errorMessage);
      setMapError(errorMessage);
    }

    return () => {
      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, [initialLocation.lng, initialLocation.lat, initialLocation.zoom]);

  useEffect(() => {
    const handleUserLocationChange = (e: CustomEvent) => {
      if (!map.current || !mapLoaded) return;

      const { lat, lng, name } = e.detail;

      if (typeof lat === "number" && typeof lng === "number") {
        console.log(`User location updated: ${name} (${lat}, ${lng})`);

        addUserLocationMarker(lat, lng, name);

        map.current.flyTo({
          center: [lng, lat],
          zoom: 15,
          speed: 1.5,
          curve: 1.2,
        });

        try {
          const bounds = new mapboxgl.LngLatBounds([lng, lat], [lng, lat]);

          bounds.extend([lng + 0.01, lat + 0.01]);
          bounds.extend([lng - 0.01, lat - 0.01]);

          setTimeout(() => {
            if (map.current) {
              map.current.fitBounds(bounds, {
                padding: { top: 50, bottom: 50, left: 50, right: 50 },
              });
            }
          }, 1000);
        } catch (error) {
          console.error("Error adjusting bounds:", error);
        }
      }
    };

    window.addEventListener(
      "mapLocationChanged",
      handleUserLocationChange as EventListener
    );

    return () => {
      window.removeEventListener(
        "mapLocationChanged",
        handleUserLocationChange as EventListener
      );
    };
  }, [mapLoaded]);

  // Add effect to handle hoveredExchangeId changes from context
  useEffect(() => {
    // Always update internal state when context state changes
    setHoveredExchangeIdInternal(hoveredExchangeId);

    // Only update marker styles if we have markers
    if (Object.keys(markerElements.current).length > 0) {
      // Update marker styles based on hover state
      Object.keys(markerElements.current).forEach((idStr) => {
        const id = parseInt(idStr, 10);
        const el = markerElements.current[id];

        if (el) {
          if (hoveredExchangeId === id) {
            // Apply hover styles
            el.classList.add("marker-hovered");
            // Bring to front
            el.style.zIndex = "10";
            // Scale up slightly
            el.style.transform = "scale(1.15)";
          } else {
            // Remove hover styles
            el.classList.remove("marker-hovered");
            el.style.zIndex = "1";
            el.style.transform = "scale(1)";
          }
        }
      });
    }
  }, [hoveredExchangeId]);

  // Update the markers creation logic
  useEffect(() => {
    if (!map.current || !mapLoaded) return;

    console.log("Adding markers to map");

    try {
      markers.current.forEach((marker) => marker.remove());
      popups.current.forEach((popup) => popup.remove());
      markers.current = [];
      popups.current = [];
      markerElements.current = {};

      // Updated coordinates for Morocco (all in Casablanca)
      const officeLocations = [
        // Casablanca downtown and surrounding areas
        { lng: -7.5898, lat: 33.5731 }, // Casablanca center
        { lng: -7.62, lat: 33.59 }, // Casablanca Maarif
        { lng: -7.635, lat: 33.5731 }, // Casablanca Ain Diab
        { lng: -7.55, lat: 33.595 }, // Casablanca Anfa
        { lng: -7.61, lat: 33.58 }, // Casablanca Gauthier
        { lng: -7.63, lat: 33.56 }, // Casablanca Bourgogne

        // More Casablanca locations
        { lng: -7.65, lat: 33.57 }, // Casablanca Sidi Maarouf
        { lng: -7.58, lat: 33.56 }, // Casablanca Mers Sultan
        { lng: -7.64, lat: 33.58 }, // Casablanca California
        { lng: -7.59, lat: 33.6 }, // Casablanca Hay Hassani

        // Additional Casablanca neighborhoods
        { lng: -7.58, lat: 33.57 }, // Near Casablanca center
        { lng: -7.615, lat: 33.585 }, // Near Maarif
        { lng: -7.625, lat: 33.575 }, // Between Maarif and Ain Diab
        { lng: -7.56, lat: 33.59 }, // Near Anfa
        { lng: -7.605, lat: 33.582 }, // Near Gauthier

        { lng: -7.57, lat: 33.58 }, // Casablanca CIL
        { lng: -7.6, lat: 33.57 }, // Casablanca Racine
        { lng: -7.62, lat: 33.57 }, // Casablanca Palmier
        { lng: -7.59, lat: 33.59 }, // Casablanca Maârif Extension
        { lng: -7.61, lat: 33.59 }, // Casablanca Gironde

        // Additional locations to ensure we have enough for all exchange offices
        { lng: -7.59, lat: 33.58 }, // Casablanca Gautier
        { lng: -7.61, lat: 33.575 }, // Casablanca Triangle d'Or
        { lng: -7.63, lat: 33.585 }, // Casablanca Ain Diab Extension
        { lng: -7.57, lat: 33.585 }, // Casablanca Habous
        { lng: -7.6, lat: 33.59 }, // Casablanca Riviera
        { lng: -7.64, lat: 33.57 }, // Casablanca Oasis
        { lng: -7.62, lat: 33.56 }, // Casablanca Racine Extension
        { lng: -7.58, lat: 33.59 }, // Casablanca 2 Mars
        { lng: -7.59, lat: 33.57 }, // Casablanca Derb Ghallef
        { lng: -7.61, lat: 33.56 }, // Casablanca Roches Noires
      ];

      const bounds = new mapboxgl.LngLatBounds();

      filteredOffices.forEach((office, index) => {
        const position =
          officeLocations[index % officeLocations.length] || officeLocations[0];

        const popupEl = document.createElement("div");
        const popupRoot = ReactDOM.createRoot(popupEl);

        // Create a compatible object for CustomMapPopup
        const popupOffice = {
          ...office,
          name: office.officeName,
          rate: office.buyRate || "0",
          hours: renderWorkingHours(office.todayWorkingHours),
          image: office.images || [],
          country: office.country?.name || "Morocco",
        };

        popupRoot.render(
          <CustomMapPopup office={popupOffice as any} position={position} />
        );

        const popup = new mapboxgl.Popup({
          offset: 25,
          closeButton: false,
          closeOnClick: false,
        }).setDOMContent(popupEl);

        const el = document.createElement("div");
        el.className = "custom-marker-container";
        el.style.transition = "all 0.2s ease";

        // Store reference to marker element
        markerElements.current[office.id] = el;

        // Check if this marker should be highlighted initially
        if (hoveredExchangeId === office.id) {
          el.classList.add("marker-hovered");
          el.style.zIndex = "10";
          el.style.transform = "scale(1.15)";
        }

        const root = ReactDOM.createRoot(el);
        root.render(
          <CustomMapMarker
            rate={office.buyRate || "0"}
            imageSrc="/assets/map-pin-logo.svg"
            isSelected={
              (selectedExchangeId !== null &&
                selectedExchangeId === office.id) ||
              hoveredExchangeId === office.id
            }
          />
        );

        const marker = new mapboxgl.Marker({
          element: el,
          anchor: "bottom",
        })
          .setLngLat(position)
          .setPopup(popup)
          .addTo(map.current!);

        el.addEventListener("dblclick", () => {
          if (office.slug) {
            window.location.href = `/exchange-detail?slug=${encodeURIComponent(
              office.slug
            )}`;
          } else {
            const params = new URLSearchParams();
            params.append("id", office.id.toString());
            params.append("name", office.officeName || "");
            params.append("rate", office.buyRate || "0");
            params.append("address", office.address || "");
            params.append(
              "hours",
              renderWorkingHours(office.todayWorkingHours)
            );
            params.append("isPopular", (office.isPopular || false).toString());
            params.append("images", (office.images || []).join(","));
            params.append("country", office.country?.name || "Morocco");

            window.location.href = `/exchange-detail?${params.toString()}`;
          }
        });

        markers.current.push(marker);
        popups.current.push(popup);

        bounds.extend(position);
      });

      // Only fit bounds if we have markers
      if (filteredOffices.length > 0) {
        map.current.fitBounds(bounds, {
          padding: 50,
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error adding markers";
      console.error("Error adding markers:", errorMessage);
      setMapError(errorMessage);
    }
  }, [
    mapLoaded,
    selectedExchangeId,
    hoveredExchangeId,
    isTouchDevice,
    isCustomFullscreen,
    filteredOffices,
  ]);

  useEffect(() => {
    if (map.current && mapLoaded) {
      setTimeout(() => {
        map.current?.resize();
      }, 100);
    }
  }, [isCustomFullscreen]);

  const handleZoom = (distance: number) => {
    if (!map.current) return;

    let zoom = 12;
    if (distance === 5) zoom = 13;
    if (distance === 1) zoom = 15;

    map.current.zoomTo(zoom, { duration: 500 });
  };

  const fetchCityName = async (lat: number, lng: number) => {
    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${mapboxgl.accessToken}&types=place&limit=1&language=fr`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch city name");
      }
      const data = await response.json();
      if (data.features && data.features.length > 0) {
        return data.features[0].text;
      }
      return "Votre position";
    } catch (error) {
      console.error("Error fetching city name:", error);
      return "Votre position";
    }
  };

  const handleUseCurrentLocation = () => {
    setLocationError(null);
    setIsSearching(true);

    if (!navigator.geolocation) {
      setLocationError("Geolocation is not supported by this browser.");
      setIsSearching(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;
        const cityName = await fetchCityName(latitude, longitude);

        if (map.current) {
          map.current.flyTo({
            center: [longitude, latitude],
            zoom: 14,
            speed: 1.5,
          });
        }

        addUserLocationMarker(latitude, longitude, cityName);

        const customEvent = new CustomEvent("mapLocationChanged", {
          detail: {
            lat: latitude,
            lng: longitude,
            name: cityName,
            country: "Maroc",
          },
        });
        window.dispatchEvent(customEvent);

        setSearchTerm("");
        setSuggestions([]);
        setShowSearch(false);
        setIsSearching(false);
      },
      (error) => {
        let errorMessage = "Unknown geolocation error";
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = "Location permission denied.";
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = "Location information is unavailable.";
            break;
          case error.TIMEOUT:
            errorMessage = "Location request timed out.";
            break;
        }
        setLocationError(errorMessage);
        setIsSearching(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      }
    );
  };

  const handleGetCurrentLocation = () => {
    if (!map.current) return;

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          map.current?.flyTo({
            center: [longitude, latitude],
            zoom: 15,
          });

          // Reverse geocode to get the place name in French
          fetch(
            `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${mapboxgl.accessToken}&language=fr&types=place`
          )
            .then((res) => res.json())
            .then((data) => {
              let placeName = "Votre position";
              if (data.features && data.features.length > 0) {
                placeName =
                  data.features[0].text_fr ||
                  data.features[0].text ||
                  placeName;
              }
              const customEvent = new CustomEvent("mapLocationChanged", {
                detail: {
                  lat: latitude,
                  lng: longitude,
                  name: placeName,
                },
              });
              window.dispatchEvent(customEvent);
            });
        },
        (error) => {
          console.error("Error getting location:", error);
        }
      );
    }
  };

  const handleFullscreen = () => {
    setIsCustomFullscreen(!isCustomFullscreen);

    const fullscreenEvent = new CustomEvent("mapFullscreenToggle", {
      detail: { isFullscreen: !isCustomFullscreen },
    });
    window.dispatchEvent(fullscreenEvent);

    setTimeout(() => {
      if (map.current) {
        map.current.resize();
      }
    }, 300);
  };

  const renderFallbackMap = () => {
    return (
      <div className="relative w-full h-full flex flex-col items-center justify-center bg-gray-50">
        <Image
          src="/assets/map-area.svg"
          alt="Static Map"
          width={800}
          height={500}
          className="w-full h-full object-cover rounded-lg"
        />
      </div>
    );
  };

  // Now update the fetchLocationSuggestions function
  const fetchLocationSuggestions = async (query: string) => {
    // Always show predefined locations, even with empty search
    if (!query.trim()) {
      setSuggestions(predefinedLocations);
      return;
    }

    setIsSearching(true);

    // First filter predefined locations
    const filteredPredefined = predefinedLocations.filter(
      (location: LocationSuggestion) =>
        location.name.toLowerCase().includes(query.toLowerCase())
    );

    // If the query is very short, just show filtered predefined locations
    if (query.length < 2) {
      setSuggestions(filteredPredefined);
      setIsSearching(false);
      return;
    }

    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
          query
        )}.json?access_token=${
          mapboxgl.accessToken
        }&types=place&limit=5&country=MA&language=fr`
      );

      if (!response.ok) {
        throw new Error(`Failed to geocode location: ${response.status}`);
      }

      const data = await response.json();

      if (data.features && data.features.length > 0) {
        const mapboxSuggestions: LocationSuggestion[] = data.features.map(
          (place: any, index: number) => {
            const country =
              place.context?.find((ctx: any) => ctx.id.startsWith("country"))
                ?.text || "Maroc";

            const [lng, lat] = place.center;

            return {
              id: `mapbox-${index}-${place.id}`,
              name: place.text,
              country: country,
              coordinates: { lng, lat },
            };
          }
        );

        // Remove duplicates (by name) with predefined cities
        const uniqueMapbox = mapboxSuggestions.filter(
          (s: LocationSuggestion) =>
            !filteredPredefined.some(
              (l: LocationSuggestion) =>
                l.name.toLowerCase() === s.name.toLowerCase()
            )
        );

        setSuggestions([...filteredPredefined, ...uniqueMapbox]);
      } else {
        setSuggestions(filteredPredefined);
      }
    } catch (error) {
      console.error("Error fetching location suggestions:", error);
      setSuggestions(filteredPredefined);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    if (!value.trim()) {
      // Show all predefined locations when search is cleared
      setSuggestions(predefinedLocations);
      return;
    }

    if (debounceTimeout) {
      clearTimeout(debounceTimeout);
    }

    const timeout = setTimeout(() => {
      fetchLocationSuggestions(value);
    }, 300);

    setDebounceTimeout(timeout);
  };

  useEffect(() => {
    return () => {
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }
    };
  }, [debounceTimeout]);

  const handleSelectLocation = (suggestion: LocationSuggestion) => {
    if (map.current) {
      map.current.flyTo({
        center: [suggestion.coordinates.lng, suggestion.coordinates.lat],
        zoom: 14,
        speed: 1.5,
      });
    }

    addUserLocationMarker(
      suggestion.coordinates.lat,
      suggestion.coordinates.lng,
      suggestion.name
    );

    const searchUpdateEvent = new CustomEvent("mapLocationChanged", {
      detail: {
        lat: suggestion.coordinates.lat,
        lng: suggestion.coordinates.lng,
        name: suggestion.name,
        country: suggestion.country,
      },
    });
    window.dispatchEvent(searchUpdateEvent);

    setSearchTerm("");
    setSuggestions([]);
    setShowSearch(false);
  };

  // Now update the handleSearch function
  const handleSearch = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    if (!searchTerm.trim()) return;

    if (suggestions.length > 0) {
      handleSelectLocation(suggestions[0]);
      return;
    }

    setIsSearching(true);

    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
          searchTerm
        )}.json?access_token=${
          mapboxgl.accessToken
        }&types=place&limit=1&country=MA&language=fr`
      );

      if (!response.ok) {
        throw new Error(`Failed to geocode location: ${response.status}`);
      }

      const data = await response.json();

      if (data.features && data.features.length > 0) {
        const feature = data.features[0];
        const [lng, lat] = feature.center;
        const locationName = feature.text;
        const country =
          feature.context?.find((ctx: any) => ctx.id.startsWith("country"))
            ?.text || "Maroc";

        if (map.current) {
          map.current.flyTo({
            center: [lng, lat],
            zoom: 14,
            speed: 1.5,
          });
        }

        addUserLocationMarker(lat, lng, locationName);

        const searchUpdateEvent = new CustomEvent("mapLocationChanged", {
          detail: {
            lat,
            lng,
            name: locationName,
            country,
          },
        });
        window.dispatchEvent(searchUpdateEvent);

        setSearchTerm("");
        setSuggestions([]);
        setShowSearch(false);
      } else {
        console.warn("No results found for search term:", searchTerm);
      }
    } catch (error) {
      console.error("Error searching for location:", error);
    } finally {
      setIsSearching(false);
    }
  };

  useEffect(() => {
    if (showSearch && searchInputRef.current) {
      searchInputRef.current.focus();
      // Show predefined locations when search is opened
      setSuggestions(predefinedLocations);
    }
  }, [showSearch]);

  useEffect(() => {
    if (!isCustomFullscreen) {
      setHoveredExchangeIdInternal(null);
      setHoveredOffice(null);
      setHoverCardPosition(null);
    }
  }, [isCustomFullscreen]);

  useEffect(() => {
    // Only show hover card if we have a selected office
    if (!hoveredOffice) {
      setHoverCardPosition(null);
      return;
    }

    // If we have a hovered office and no position yet, calculate position
    if (hoveredOffice && !hoverCardPosition && hoveredExchangeIdInternal) {
      const el = markerElements.current[hoveredExchangeIdInternal];
      if (el && mapContainer.current) {
        const rect = el.getBoundingClientRect();
        const mapRect = mapContainer.current.getBoundingClientRect();

        let left = rect.left - mapRect.left;
        let top = rect.top - mapRect.top - 350;

        const cardWidth = 250;

        if (left < 10) left = 10;
        if (left + cardWidth > mapRect.width - 10) {
          left = mapRect.width - cardWidth - 10;
        }

        if (top < 10) {
          top = rect.bottom - mapRect.top + 10;
        }

        setHoverCardPosition({ top, left });
      }
    }
  }, [
    hoveredOffice,
    hoverCardPosition,
    hoveredExchangeIdInternal,
    selectedExchangeId,
  ]);

  return (
    <div
      className={`relative w-full h-full ${
        isCustomFullscreen ? "fixed top-0 left-0 right-0 bottom-0 z-50" : ""
      }`}
      style={{
        ...(isCustomFullscreen && {
          top: 0,
          left: 0,
          width: "100vw",
          height: "100vh",
        }),
      }}
    >
      {mapError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md">
            <h3 className="text-lg font-semibold text-red-600 mb-2">
              Map Error
            </h3>
            <p className="text-gray-700">{mapError}</p>
            <p className="text-gray-600 mt-2 text-sm">
              Please try refreshing the page or using a different browser.
            </p>
          </div>
        </div>
      )}

      {!mapLoaded && !mapError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-20">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-green-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <p className="text-gray-500 text-sm">Loading map...</p>
          </div>
        </div>
      )}

      {/* No Results Message */}
      {/* {filteredOffices.length === 0 && mapLoaded && !mapError && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white p-4 rounded-lg shadow-lg z-10 text-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 mx-auto mb-2 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M12 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <h3 className="text-lg font-medium text-gray-800">
            No Results Found
          </h3>
          <p className="text-gray-600 mt-1">
            Try adjusting your filters to see more offices
          </p>
        </div>
      )} */}

      <div
        ref={mapContainer}
        className="absolute inset-0 w-full h-full overflow-hidden"
        style={{
          display: mapError ? "none" : "block",
        }}
      />

      <div
        className="absolute top-4 right-4 z-10 flex items-center"
        ref={searchContainerRef}
      >
        {showSearch && (
          <div className="mr-2 animate-fadeIn relative">
            <form onSubmit={handleSearch}>
              <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                  <Image
                    src="/assets/location-icon.svg"
                    alt="location"
                    width={18}
                    height={18}
                  />
                </div>
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search city..."
                  className="w-[220px] md:w-[320px] h-12 pl-12 pr-12 rounded-xl bg-white border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#20523C] focus:border-transparent text-gray-700 placeholder-gray-400"
                />
                {searchTerm && (
                  <button
                    type="button"
                    onClick={() => {
                      setSearchTerm("");
                      setSuggestions(predefinedLocations);
                    }}
                    className="absolute right-3 top-1/2 w-[16px] h-[16px] transform -translate-y-1/2 rounded-full flex items-center justify-center transition-colors cursor-pointer"
                  >
                    <Image
                      src="/assets/close-circle.svg"
                      alt="close"
                      width={16}
                      height={16}
                    />
                  </button>
                )}
              </div>
            </form>

            {/* Dropdown Menu */}
            {showSearch && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-lg border border-gray-100 z-50 w-[320px] overflow-hidden">
                {/* Use Current Location Button */}
                <button
                  onClick={handleUseCurrentLocation}
                  className="w-full flex items-center gap-2 px-3 py-2.5 text-left text-[#20523C] hover:bg-[#F1F1F1] transition-colors border-b border-[#DEDEDE]"
                >
                  <Image
                    src="/assets/gps.svg"
                    width={18}
                    height={18}
                    alt="gps"
                  />
                  <span className="text-[14px] leading-[20px] font-medium">
                    Use my current location
                  </span>
                </button>

                <div className="max-h-60 overflow-y-auto">
                  {isSearching ? (
                    <div className="flex items-center justify-center py-3">
                      <div className="w-5 h-5 border-2 border-[#20523C] border-t-transparent rounded-full animate-spin"></div>
                      <span className="ml-2 text-[#585858] text-[14px]">
                        Searching...
                      </span>
                    </div>
                  ) : suggestions.length > 0 ? (
                    suggestions.map((suggestion, index) => (
                      <button
                        key={suggestion.id}
                        onClick={() => handleSelectLocation(suggestion)}
                        className={`w-full flex items-center px-4 py-3 hover:bg-gray-50 text-left transition-colors ${
                          index === 0 ? "bg-gray-100" : ""
                        } ${
                          index !== suggestions.length - 1
                            ? "border-b border-gray-100"
                            : ""
                        }`}
                      >
                        <div className="flex flex-col w-full">
                          <span className="text-gray-700 text-[15px] font-medium">
                            {suggestion.name}
                          </span>
                          {suggestion.country && (
                            <span className="text-gray-500 text-[13px] mt-0.5">
                              {suggestion.country}
                            </span>
                          )}
                        </div>
                      </button>
                    ))
                  ) : searchTerm && !isSearching ? (
                    <div className="px-4 py-3 text-gray-500">
                      No locations found
                    </div>
                  ) : null}
                </div>
              </div>
            )}
          </div>
        )}
        <button
          onClick={() => {
            setShowSearch(!showSearch);
            if (!showSearch) {
              setSuggestions(predefinedLocations);
            } else {
              setSuggestions([]);
            }
          }}
          className="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-shadow"
          aria-label="Search"
        >
          {showSearch ? (
            <svg
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 6L6 18"
                stroke="#333"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M6 6L18 18"
                stroke="#333"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ) : (
            <svg
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z"
                stroke="#333"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M21 21L16.65 16.65"
                stroke="#333"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          )}
        </button>
      </div>

      <div
        className={`absolute space-y-[10px] ${
          isCustomFullscreen ? "bottom-[130px]" : "bottom-4"
        }  right-4 z-10`}
      >
        {/* Zoom controls */}
        <div className=" flex flex-col gap-2">
          {/* Location button */}
          <button
            onClick={handleGetCurrentLocation}
            className="w-10 cursor-pointer h-10 bg-white rounded-sm flex items-center justify-center shadow-md"
            aria-label="Get current location"
          >
            <Image src="/assets/gps.svg" alt="gps" width={24} height={24} />
          </button>
          <div className="bg-white rounded-md shadow-md">
            <button
              onClick={() => map.current?.zoomIn()}
              className="w-10 h-10 flex cursor-pointer items-center justify-center"
              aria-label="Zoom in"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 5V19"
                  stroke="#333"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M5 12H19"
                  stroke="#333"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
              </svg>
            </button>
            <hr className="border-t mx-2.5 border-[#DDDDDD]" />
            <button
              onClick={() => map.current?.zoomOut()}
              className="w-10 cursor-pointer h-10 flex items-center justify-center"
              aria-label="Zoom out"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 12H19"
                  stroke="#333"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Distance filters */}
        <div className=" transform  flex items-center gap-[10px] z-10">
          {/* <button
          onClick={() => handleZoom(10)}
          className="px-3 hidden md:block cursor-pointer py-1.5 w-auto h-[40px] bg-white rounded-md text-[16px] font-medium shadow-md "
        >
          10Km
        </button>
        <button
          onClick={() => handleZoom(5)}
          className="px-3 cursor-pointer py-1.5 w-auto h-[40px] bg-white rounded-md text-[16px] font-medium shadow-md "
        >
          5Km
        </button>
        <button
          onClick={() => handleZoom(1)}
          className="px-3 cursor-pointer py-1.5 w-auto h-[40px] bg-white rounded-md text-[16px] font-medium shadow-md "
        >
          1Km
        </button>
        <button className="px-3 hidden md:block cursor-pointer py-1.5 w-[40px] h-[40px] bg-white rounded-md text-[16px] font-medium shadow-md ">
          ...
        </button> */}
          <button
            onClick={handleFullscreen}
            className="w-[40px] hidden md:flex cursor-pointer h-[40px] bg-white rounded-md  items-center justify-center shadow-md"
            aria-label={
              isCustomFullscreen ? "Exit fullscreen" : "Enter fullscreen"
            }
          >
            {isCustomFullscreen ? (
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 0 2-2h3M3 16h3a2 2 0 0 0 2 2v3"
                  stroke="#333"
                  strokeWidth="2"
                />
              </svg>
            ) : (
              <Image
                src="/assets/maximize.svg"
                alt="Fullscreen"
                width={24}
                height={24}
              />
            )}
          </button>
        </div>
      </div>

      {hoveredOffice && hoverCardPosition && (
        <div
          className="absolute z-40 w-[250px] transform origin-bottom-left transition-all duration-200 ease-out"
          style={{
            top: `${hoverCardPosition.top}px`,
            left: `${hoverCardPosition.left}px`,
            opacity: 1,
            scale: "1",
            boxShadow: "0 10px 25px rgba(0,0,0,0.15)",
          }}
        >
          <button
            className="absolute -top-2 -right-2 w-8 h-8 bg-white border border-gray-200 rounded-full shadow-md flex items-center justify-center z-50"
            onClick={() => {
              setHoveredExchangeIdInternal(null);
              setHoveredOffice(null);
              setHoverCardPosition(null);
              setSelectedExchangeId(null);
            }}
          >
            <svg
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#333333"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
          <HoverProvider>
            <ExchangeCard
              id={hoveredOffice.id}
              name={hoveredOffice.officeName || ""}
              rate={hoveredOffice.buyRate || "0"}
              address={hoveredOffice.address || ""}
              hours={renderWorkingHours(hoveredOffice.todayWorkingHours)}
              images={hoveredOffice.images || []}
              isPopular={hoveredOffice.isPopular || false}
              country={hoveredOffice.country?.name || "Morocco"}
            />
          </HoverProvider>
        </div>
      )}

      <style jsx global>{`
        .mapboxgl-ctrl-logo {
          display: none !important;
        }
        .mapboxgl-ctrl-attrib {
          display: none !important;
        }
        .mapboxgl-ctrl-bottom-right {
          bottom: 110px !important;
          right: 15px !important;
        }
        .mapboxgl-ctrl-group {
          background-color: transparent !important;
          box-shadow: none !important;
        }
        .mapboxgl-ctrl-group button {
          display: none !important;
        }
        .mapboxgl-popup {
          z-index: 5;
        }
        .mapboxgl-popup-content {
          border-radius: 8px;
          padding: 12px;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .marker-hovered {
          filter: drop-shadow(0 0 8px rgba(32, 82, 60, 0.8));
        }
        .custom-marker-container {
          transition: transform 0.2s ease, filter 0.2s ease;
        }
      `}</style>
    </div>
  );
};

export default MapView;
