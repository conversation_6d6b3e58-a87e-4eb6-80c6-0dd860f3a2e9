import { ExchangeOffice, MapMarker } from "./types";

// Define all available currencies
const ALL_CURRENCIES = [
  "USD",
  "EUR",
  "AUD",
  "CAD",
  "GBP",
  "CHF",
  "SAR",
  "QAR",
  "KWD",
  "CNY",
  "TRY",
  "JPY",
  "NOK",
  "KRW",
  "RUB",
  "INR",
];

// Helper function to get random currencies
const getRandomCurrencies = (min: number, max: number) => {
  const shuffled = [...ALL_CURRENCIES].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, Math.floor(Math.random() * (max - min + 1)) + min);
};

// Helper function to determine if an office is currently open
const isOfficeOpen = (hours: string): boolean => {
  // Parse hours string like "08:00 - 16:00"
  const [openTime, closeTime] = hours.split(" - ");
  const [openHour] = openTime.split(":").map(Number);
  const [closeHour] = closeTime.split(":").map(Number);

  // Get current hour (0-23)
  const currentHour = new Date().getHours();

  // Check if current hour is within opening hours
  return currentHour >= openHour && currentHour < closeHour;
};

export const exchangeOffices: ExchangeOffice[] = [
  {
    id: 1,
    officeName: "Atlas Exchange",
    buyRate: "16430",
    address: "123 Boulevard d'Anfa, Casablanca 20000, Morocco",
    todayWorkingHours: "08:00 - 16:00",
    images: [
      "/assets/results/card1.png",
      "/assets/results/card2.png",
      "/assets/results/card3.png",
      "/assets/results/card4.png",
      "/assets/results/card5.png",
      "/assets/results/card6.png",
    ],
    country: { name: "Morocco" },
    isPopular: true,
    availableCurrencies: ["USD", "EUR", "GBP", "CHF", "SAR", "QAR", "KWD"],
    searchCount: 450,
    distance: 0.8,
    isCurrentlyOpen: isOfficeOpen("08:00 - 16:00"),
  },
  {
    id: 2,
    officeName: "DirhamX",
    buyRate: "16450",
    address: "45 Boulevard Mohammed V, Casablanca 20250, Morocco",
    todayWorkingHours: "07:00 - 20:00",
    images: [
      "/assets/results/card2.png",
      "/assets/results/card3.png",
      "/assets/results/card1.png",
      "/assets/results/card4.png",
      "/assets/results/card5.png",
      "/assets/results/card6.png",
    ],
    country: { name: "Morocco" },
    isPopular: true,
    availableCurrencies: ["USD", "EUR", "AUD", "CAD", "GBP", "CHF"],
    searchCount: 620,
    distance: 1.2,
    isCurrentlyOpen: isOfficeOpen("07:00 - 20:00"),
  },
  {
    id: 3,
    officeName: "Sahara Exchange",
    buyRate: "16512",
    address: "75 Boulevard Zerktouni, Casablanca 20250, Morocco",
    todayWorkingHours: "09:00 - 22:00",
    images: [
      "/assets/results/card3.png",
      "/assets/results/card4.png",
      "/assets/results/card1.png",
      "/assets/results/card2.png",
      "/assets/results/card5.png",
      "/assets/results/card6.png",
    ],
    country: { name: "Morocco" },
    isPopular: true,
    availableCurrencies: [
      "USD",
      "EUR",
      "GBP",
      "JPY",
      "CNY",
      "TRY",
      "KWD",
      "SAR",
    ],
    searchCount: 580,
    distance: 1.5,
    isCurrentlyOpen: isOfficeOpen("09:00 - 22:00"),
  },
  {
    id: 4,
    officeName: "Golden Dirham",
    buyRate: "16512",
    address: "18 Rue Jean Jaurès, Casablanca 20100, Morocco",
    todayWorkingHours: "09:00 - 14:30",
    images: [
      "/assets/results/card4.png",
      "/assets/results/card5.png",
      "/assets/results/card1.png",
      "/assets/results/card2.png",
      "/assets/results/card3.png",
      "/assets/results/card6.png",
    ],
    country: { name: "Morocco" },
    isPopular: true,
    availableCurrencies: ["USD", "EUR", "GBP", "CHF", "KWD"],
    searchCount: 320,
    distance: 0.5,
    isCurrentlyOpen: isOfficeOpen("09:00 - 14:30"),
  },
  {
    id: 5,
    officeName: "Oasis Currency",
    buyRate: "16100",
    address: "14 Rue Ibnou Sina, Casablanca 20100, Morocco",
    todayWorkingHours: "10:00 - 17:00",
    images: [
      "/assets/results/card5.png",
      "/assets/results/card6.png",
      "/assets/results/card2.png",
      "/assets/results/card1.png",
      "/assets/results/card3.png",
      "/assets/results/card4.png",
    ],
    country: { name: "Morocco" },
    isPopular: true,
    availableCurrencies: ["USD", "EUR", "CAD", "RUB", "INR", "NOK"],
    searchCount: 210,
    distance: 2.1,
    isCurrentlyOpen: isOfficeOpen("10:00 - 17:00"),
  },
  {
    id: 6,
    officeName: "Casablanca Forex",
    buyRate: "16550",
    address: "32 Boulevard Moulay Youssef, Casablanca 20070, Morocco",
    todayWorkingHours: "06:00 - 22:00",
    images: [
      "/assets/results/card6.png",
      "/assets/results/card1.png",
      "/assets/results/card2.png",
      "/assets/results/card3.png",
      "/assets/results/card4.png",
      "/assets/results/card5.png",
    ],
    country: { name: "Morocco" },
    isPopular: true,
    availableCurrencies: [
      "USD",
      "EUR",
      "GBP",
      "CHF",
      "JPY",
      "KRW",
      "CNY",
      "TRY",
      "SAR",
      "QAR",
      "KWD",
    ],
    searchCount: 780,
    distance: 1.8,
    isCurrentlyOpen: isOfficeOpen("06:00 - 22:00"),
  },
  {
    id: 7,
    officeName: "Desert Trade",
    buyRate: "16420",
    address: "200 Boulevard d'Anfa, Casablanca 20000, Morocco",
    todayWorkingHours: "08:30 - 17:30",
    images: [
      "/assets/results/card1.png",
      "/assets/results/card3.png",
      "/assets/results/card2.png",
      "/assets/results/card4.png",
      "/assets/results/card5.png",
      "/assets/results/card6.png",
    ],
    country: { name: "Morocco" },
    isPopular: false,
    availableCurrencies: ["USD", "EUR", "AUD", "GBP"],
    searchCount: 150,
    distance: 3.0,
    isCurrentlyOpen: isOfficeOpen("08:30 - 17:30"),
  },
  {
    id: 8,
    officeName: "Mirage Money",
    buyRate: "16530",
    address: "55 Boulevard Zerktouni, Casablanca 20100, Morocco",
    todayWorkingHours: "09:00 - 18:00",
    images: [
      "/assets/results/card2.png",
      "/assets/results/card4.png",
      "/assets/results/card1.png",
      "/assets/results/card3.png",
      "/assets/results/card5.png",
      "/assets/results/card6.png",
    ],
    country: { name: "Morocco" },
    isPopular: true,
    availableCurrencies: ["USD", "EUR", "GBP", "CHF", "SAR", "QAR"],
    searchCount: 420,
    distance: 0.9,
    isCurrentlyOpen: isOfficeOpen("09:00 - 18:00"),
  },
  {
    id: 9,
    officeName: "Sunset Exchange",
    buyRate: "16470",
    address: "5 Rue Ahmed El Mokri, Casablanca 20200, Morocco",
    todayWorkingHours: "07:30 - 19:00",
    images: [
      "/assets/results/card3.png",
      "/assets/results/card5.png",
      "/assets/results/card1.png",
      "/assets/results/card2.png",
      "/assets/results/card4.png",
      "/assets/results/card6.png",
    ],
    country: { name: "Morocco" },
    isPopular: false,
    availableCurrencies: ["USD", "EUR", "CAD"],
    searchCount: 180,
    distance: 1.7,
    isCurrentlyOpen: isOfficeOpen("07:30 - 19:00"),
  },
  {
    id: 10,
    officeName: "Nile Currency",
    buyRate: "16500",
    address: "27 Boulevard d'Anfa, Casablanca 20050, Morocco",
    todayWorkingHours: "08:00 - 20:00",
    images: [
      "/assets/results/card4.png",
      "/assets/results/card6.png",
      "/assets/results/card1.png",
      "/assets/results/card2.png",
      "/assets/results/card3.png",
      "/assets/results/card5.png",
    ],
    country: { name: "Morocco" },
    isPopular: true,
    availableCurrencies: ["USD", "EUR", "GBP", "CHF", "AUD", "CAD", "JPY"],
    searchCount: 520,
    distance: 1.3,
    isCurrentlyOpen: isOfficeOpen("08:00 - 20:00"),
  },
  // Additional entries with randomized properties
  ...Array.from({ length: 8 }).map((_, i) => ({
    id: 11 + i,
    officeName: `Exchange ${11 + i}`,
    buyRate: `${16300 + i * 10}`,
    address: `${10 + i} Avenue des FAR, Casablanca 20000, Morocco`,
    todayWorkingHours: "09:00 - 19:00",
    images: [
      "/assets/results/card1.png",
      "/assets/results/card2.png",
      "/assets/results/card3.png",
      "/assets/results/card4.png",
      "/assets/results/card5.png",
      "/assets/results/card6.png",
    ],
    country: { name: "Morocco" },
    isPopular: i % 2 === 0,
    availableCurrencies: getRandomCurrencies(3, 8),
    searchCount: Math.floor(Math.random() * 500) + 100,
    distance: Math.round((Math.random() * 5 + 0.5) * 10) / 10,
    isCurrentlyOpen: isOfficeOpen("09:00 - 19:00"),
  })),
];

export const mapMarkers: MapMarker[] = [
  { id: 1, position: { top: "25%", left: "70%" }, label: "RP16430" },
  { id: 2, position: { top: "40%", left: "60%" }, label: "RP16450" },
  { id: 3, position: { top: "55%", left: "75%" }, label: "RP16512" },
  { id: 4, position: { top: "65%", left: "65%" }, label: "RP16512" },
  { id: 5, position: { top: "75%", left: "55%" }, label: "RP16100" },
  { id: 6, position: { top: "85%", left: "70%" }, label: "RP16550" },
  { id: 7, position: { top: "30%", left: "50%" }, label: "RP16420" },
  { id: 8, position: { top: "45%", left: "80%" }, label: "RP16530" },
  { id: 9, position: { top: "60%", left: "45%" }, label: "RP16470" },
  { id: 10, position: { top: "70%", left: "60%" }, label: "RP16500" },
  { id: 11, position: { top: "20%", left: "55%" }, label: "RP16480" },
  { id: 12, position: { top: "35%", left: "65%" }, label: "RP16520" },
  { id: 13, position: { top: "50%", left: "70%" }, label: "RP16440" },
  { id: 14, position: { top: "65%", left: "50%" }, label: "RP16510" },
  { id: 15, position: { top: "80%", left: "60%" }, label: "RP16460" },
  { id: 16, position: { top: "25%", left: "75%" }, label: "RP16540" },
  { id: 17, position: { top: "40%", left: "55%" }, label: "RP16490" },
  { id: 18, position: { top: "55%", left: "65%" }, label: "RP16515" },
  { id: 19, position: { top: "70%", left: "70%" }, label: "RP16425" },
  { id: 20, position: { top: "85%", left: "50%" }, label: "RP16525" },
  { id: 21, position: { top: "30%", left: "60%" }, label: "RP16455" },
  { id: 22, position: { top: "45%", left: "70%" }, label: "RP16505" },
  { id: 23, position: { top: "60%", left: "55%" }, label: "RP16475" },
  { id: 24, position: { top: "75%", left: "65%" }, label: "RP16535" },
  { id: 25, position: { top: "20%", left: "50%" }, label: "RP16435" },
  { id: 26, position: { top: "35%", left: "75%" }, label: "RP16545" },
  { id: 27, position: { top: "50%", left: "60%" }, label: "RP16465" },
  { id: 28, position: { top: "65%", left: "70%" }, label: "RP16555" },
  { id: 29, position: { top: "80%", left: "55%" }, label: "RP16485" },
  { id: 30, position: { top: "15%", left: "15%" }, label: "RP16560" },
];
