import React, { useState, useEffect } from "react";
import ExchangeCard from "./ExchangeCard";
import { exchangeOffices } from "./data";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { ExchangeOffice, WorkingHoursObject } from "./types";
import { truncateName } from "./ExchangeCard";
import * as api from "../../services/api";

// Helper function to convert working hours to string
const formatWorkingHours = (hours: string | WorkingHoursObject): string => {
  if (typeof hours === "string") return hours;

  if (hours.fromTime && hours.toTime) {
    return `${hours.fromTime} - ${hours.toTime}`;
  }

  return "Hours not specified";
};

const ExchangeCardSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-lg border border-[#DEDEDE] w-full">
      <div className="relative">
        <Skeleton height={121} className="rounded-t-lg" />
        <div className="absolute top-3 left-3 z-30">
          <Skeleton width={100} height={24} borderRadius={4} />
        </div>
        <div className="absolute top-3 right-3 z-30">
          <Skeleton width={60} height={24} borderRadius={4} />
        </div>
      </div>

      <div className="px-[18.25px] pt-[18.25px] sm:p-[18.25px]">
        <Skeleton height={19} width="70%" />
        <Skeleton height={24} width="50%" className="mt-1" />
        <Skeleton height={20} width="90%" className="mt-[12.17px]" />
        <Skeleton height={20} width="80%" className="mt-1" />
      </div>

      <div className="mt-6 flex items-center justify-between gap-[12.17px] p-[18.25px] pt-0">
        <Skeleton height={47} width="100%" borderRadius={6} />
        <Skeleton height={47} width={47} borderRadius={6} />
      </div>
    </div>
  );
};

interface ExchangeListProps {
  cityOffices?: {
    offices: ExchangeOffice[];
    totalCount: number;
    cityInfo: {
      name: string;
      totalOffices: number;
      activeOffices: number;
      verifiedOffices: number;
      featuredOffices: number;
      availableCurrencies: string[];
    };
    pagination: {
      page: number;
      limit: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  } | null;
}

const ExchangeList: React.FC<ExchangeListProps> = ({ cityOffices }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [displayedOffices, setDisplayedOffices] = useState<ExchangeOffice[]>(
    []
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const initialBatchSize = 9;
  const loadMoreBatchSize = 9;

  useEffect(() => {
    // Reset state when cityOffices prop changes
    setIsLoading(true);
    setCurrentPage(1);
    setDisplayedOffices([]);

    // Initial loading
    const timer = setTimeout(() => {
      setIsLoading(false);
      const offices = cityOffices?.offices || [];
      setDisplayedOffices(offices.slice(0, initialBatchSize));
      setTotalPages(cityOffices?.pagination?.totalPages || 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [cityOffices]);

  const loadMoreOffices = async () => {
    if (currentPage >= totalPages) return;

    setIsLoadingMore(true);

    try {
      // Extract city name from current cityOffices or use a default
      const cityName =
        cityOffices?.cityInfo?.name?.toLowerCase() || "casablanca";

      // Fetch next page with existing filters
      const nextPage = currentPage + 1;
      const nextPageData = await api.fetchCityOffices({
        cityName,
        page: nextPage,
        limit: loadMoreBatchSize,
        // Reapply filters from initial fetch
        availableCurrencies: cityOffices?.offices[0]?.availableCurrencies,
        trend: cityOffices?.offices[0]?.isPopular ? "featured" : undefined,
        showOnlyOpenNow: cityOffices?.offices.every(
          (office) => office.isCurrentlyOpen
        ),
      });

      // Append new offices to existing list
      const updatedOffices = [
        ...displayedOffices,
        ...(nextPageData.offices || []),
      ];

      setDisplayedOffices(updatedOffices);
      setCurrentPage(nextPage);
      setTotalPages(nextPageData.pagination?.totalPages || totalPages);
    } catch (error) {
      console.error("Error loading more offices:", error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  const renderSkeletons = (count: number) => {
    return Array.from({ length: count }).map((_, index) => (
      <ExchangeCardSkeleton key={`skeleton-${index}`} />
    ));
  };

  const shouldShowLoadMore =
    displayedOffices.length >= initialBatchSize && currentPage < totalPages;

  return (
    <div className="mt-4 mb-6 w-full">
      <div className="w-full grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {isLoading ? (
          renderSkeletons(initialBatchSize)
        ) : !cityOffices || cityOffices.offices.length === 0 ? (
          <div className="col-span-full flex flex-col items-center justify-center py-12">
            <div className="text-gray-400 text-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 mx-auto mb-4 text-gray-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M12 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <h3 className="text-xl font-medium mb-2">
                No Exchange Offices Found
              </h3>
              <p className="text-gray-500">
                There are no exchange offices available in this location.
              </p>
              <p className="text-gray-500 mt-1">
                Try searching in Casablanca where our offices are located.
              </p>
            </div>
          </div>
        ) : (
          <>
            {displayedOffices.map((office) => (
              <ExchangeCard
                key={office.id}
                id={office.id}
                name={truncateName(office.officeName || "Unnamed Office")}
                rate={office.buyRate || "N/A"}
                address={office.address || "Address not available"}
                hours={formatWorkingHours(office.todayWorkingHours)}
                images={office.images || []}
                country={office.country.name || "Morocco"}
                isPopular={office.isPopular}
                availableCurrencies={office.availableCurrencies}
                searchCount={office.searchCount}
                distance={office.distance}
                isCurrentlyOpen={office.isCurrentlyOpen}
                slug={office.slug} // Add slug here
              />
            ))}
            {isLoadingMore && renderSkeletons(loadMoreBatchSize)}
          </>
        )}
      </div>

      {/* Minimal Load More Button */}
      {shouldShowLoadMore && !isLoadingMore && (
        <div className="flex justify-center mt-8">
          <button
            onClick={loadMoreOffices}
            className="px-4 py-2 text-[#20523C] text-sm font-medium border border-[#20523C] rounded-md hover:bg-[#20523C] hover:text-white transition-colors duration-200"
          >
            Load more
          </button>
        </div>
      )}
    </div>
  );
};

export default ExchangeList;
