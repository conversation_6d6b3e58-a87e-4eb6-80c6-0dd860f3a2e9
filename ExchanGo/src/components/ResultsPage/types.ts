export interface WorkingHoursObject {
  dayOfWeek: string;
  isActive: boolean;
  fromTime: string;
  toTime: string;
  hasBreak: boolean;
  breakFromTime?: string;
  breakToTime?: string;
  officeId: number;
  createdAt: string;
  updatedAt: string;
}

export interface ExchangeOffice {
  id: number;
  officeName?: string;
  buyRate?: string;
  address?: string;
  todayWorkingHours: string | WorkingHoursObject;
  images?: Array<{ id: string; path: string }> | string[]; // Changed from 'image'
  country: {
    name: string;
  };
  isPopular?: boolean;
  availableCurrencies?: string[];
  searchCount?: number;
  distance?: number;
  isCurrentlyOpen?: boolean;
  slug?: string; // Add slug here
}

export interface MapMarker {
  id: number;
  position: {
    top: string;
    left: string;
  };
  label: string;
}

export interface SearchBarProps {
  location?: string;
  amount?: string;
  sourceCurrency?: string;
  targetCurrency?: string;
  onSearch?: () => void;
}

export interface FilterState {
  selectedCurrencies: string[];
  selectedTrends: string[];
  showOpenOfficesOnly: boolean;
}

export interface FilterSortProps {
  count?: number;
  location?: string;
  lastUpdate?: string;
  onFilter?: () => void;
  onSort?: (option: string) => void;
  onRefresh?: () => void;
  onApplyFilters?: (filters: FilterState) => void;
  onClearFilters?: () => void;
}
