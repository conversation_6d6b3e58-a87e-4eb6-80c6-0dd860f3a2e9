@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap");

@import "tailwindcss";

html {
  -webkit-text-size-adjust: 100%;
  scroll-behavior: smooth;
}

body {
  font-family: "DM Sans", sans-serif !important;
}

.dm-sans {
  font-family: "DM Sans", sans-serif !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.hide-scrollbar {
  overflow: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-time-icon::-webkit-calendar-picker-indicator {
  display: none;
  -webkit-appearance: none;
}

.swiper-pagination-bullet.swiper-pagination-bullet-custom {
  width: 6px;
  height: 6px;
  border-radius: 9999px;
  background: white;
  opacity: 50%;
}

.swiper-pagination-bullet.swiper-pagination-bullet-active.swiper-pagination-bullet-active-custom {
  background: #ffffff;
  opacity: 1;
  border-radius: 9999px;
  width: 6px;
  height: 6px;
}

/* Animation for search input in MapView */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

/* User location marker pulse effect */
.user-location-marker {
  position: relative;
}

.user-location-marker::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  background-color: rgba(75, 151, 248, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}


@keyframes slide-up {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}
.animate-slide-up {
  animation: slide-up 0.3s ease;
}