"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import { JSX } from "@emotion/react/jsx-runtime";
import AdminLayout from "@/components/AdminComponents/AdminLayout";
import AboutExchangeOffice from "@/components/AdminComponents/Analytics/AboutExchangeOffice";
import OfficeEngagement from "@/components/AdminComponents/Analytics/OfficeEngagement";
import OverallActivity from "@/components/AdminComponents/Analytics/OverallActivity";
import StatsCard from "@/components/AdminComponents/Analytics/StatsCard";
import HistoryDropdown from "@/components/ExchangeLeadboard/HistoryDropdown";
import AboutOfficeFilterModal from "@/components/AdminComponents/Analytics/AboutOfficeFilterModal";
import OfficeEngagementFilterModal from "@/components/AdminComponents/Analytics/OfficeEngagementFilterModal";
import TableWrapper from "@/components/AdminComponents/Analytics/TableWrapper";

interface HistoryOption {
  value: string;
  label: string;
}

interface TabType {
  id: string;
  label: string;
}

interface AboutOfficeActivityData {
  officeName: string;
  city: string;
  country: string;
  registrationDate: string;
  status: "Validated" | "Pending" | "Rejected";
  duration: string;
  registeredCount: string;
}

interface OfficeEngagementActivityData {
  officeName: string;
  city: string;
  profileViews: number;
  phoneCalls: number;
  gpsClick: number;
  share: number;
  waAlerts: number;
}

interface OverallActivityData {
  officeName: string;
  city: string;
  lastUpdate: string;
  updates7days: number;
  activityStatus: "Very Active" | "Active" | "Low Activity";
}

const AboutOfficeOriginalData: AboutOfficeActivityData[] = [
  {
    officeName: "Atlas Exchange",
    city: "Rabat",
    country: "Pakistan",
    registrationDate: "January 15, 2025",
    status: "Validated",
    duration: "22 Days",
    registeredCount: "15 Times",
  },
  {
    officeName: "Sahara Forex",
    city: "Marrakech",
    country: "Morocco",
    registrationDate: "February 20, 2025",
    status: "Pending",
    duration: "18 Days",
    registeredCount: "12 Times",
  },
  {
    officeName: "DirhamX",
    city: "Tangier",
    country: "Morocco",
    registrationDate: "March 25, 2025",
    status: "Rejected",
    duration: "6 Days",
    registeredCount: "3 Times",
  },
  {
    officeName: "Maghreb Cash",
    city: "Agadir",
    country: "Morocco",
    registrationDate: "April 30, 2025",
    status: "Validated",
    duration: "5 Days",
    registeredCount: "1 Times",
  },
  {
    officeName: "RabatX",
    city: "Fez",
    country: "Morocco",
    registrationDate: "July 15, 2025",
    status: "Validated",
    duration: "9 Days",
    registeredCount: "4 Times",
  },
  {
    officeName: "Casablanca Trade",
    city: "Casablanca",
    country: "Morocco",
    registrationDate: "May 5, 2025",
    status: "Pending",
    duration: "11 Days",
    registeredCount: "8 Times",
  },
  {
    officeName: "Moroccan Gold",
    city: "Essaouira",
    country: "Morocco",
    registrationDate: "August 20, 2025",
    status: "Validated",
    duration: "15 Days",
    registeredCount: "10 Times",
  },
  {
    officeName: "Tanger Currency",
    city: "Ouarzazate",
    country: "Morocco",
    registrationDate: "June 10, 2025",
    status: "Rejected",
    duration: "25 Days",
    registeredCount: "20 Times",
  },
  {
    officeName: "Medina Money",
    city: "Casablanca",
    country: "Morocco",
    registrationDate: "April 15, 2025",
    status: "Validated",
    duration: "20 Days",
    registeredCount: "13 Times",
  },
  {
    officeName: "Rif Exchange",
    city: "Tetouan",
    country: "Morocco",
    registrationDate: "March 10, 2025",
    status: "Pending",
    duration: "14 Days",
    registeredCount: "6 Times",
  },
  {
    officeName: "Souss Cash",
    city: "Agadir",
    country: "Morocco",
    registrationDate: "January 30, 2025",
    status: "Rejected",
    duration: "3 Days",
    registeredCount: "2 Times",
  },
  {
    officeName: "Atlas Forex",
    city: "Rabat",
    country: "Morocco",
    registrationDate: "February 5, 2025",
    status: "Validated",
    duration: "17 Days",
    registeredCount: "11 Times",
  },
  {
    officeName: "Meknes Trade",
    city: "Meknes",
    country: "Morocco",
    registrationDate: "June 25, 2025",
    status: "Pending",
    duration: "8 Days",
    registeredCount: "5 Times",
  },
  {
    officeName: "Oasis Exchange",
    city: "Marrakech",
    country: "Morocco",
    registrationDate: "May 20, 2025",
    status: "Validated",
    duration: "12 Days",
    registeredCount: "9 Times",
  },
  {
    officeName: "Fes Money",
    city: "Fez",
    country: "Morocco",
    registrationDate: "April 1, 2025",
    status: "Rejected",
    duration: "7 Days",
    registeredCount: "7 Times",
  },
];

const OfficeEngagementOriginalData: OfficeEngagementActivityData[] = [
  {
    officeName: "Atlas Exchange",
    city: "Rabat",
    profileViews: 218,
    phoneCalls: 523,
    gpsClick: 1673,
    share: 15,
    waAlerts: 15,
  },
  {
    officeName: "Sahara Forex",
    city: "Marrakech",
    profileViews: 243,
    phoneCalls: 132,
    gpsClick: 2933,
    share: 12,
    waAlerts: 12,
  },
  {
    officeName: "DirhamX",
    city: "Tangier",
    profileViews: 300,
    phoneCalls: 324,
    gpsClick: 1789,
    share: 3,
    waAlerts: 3,
  },
  {
    officeName: "Maghreb Cash",
    city: "Agadir",
    profileViews: 184,
    phoneCalls: 356,
    gpsClick: 1803,
    share: 1,
    waAlerts: 1,
  },
  {
    officeName: "RabatX",
    city: "Fez",
    profileViews: 138,
    phoneCalls: 402,
    gpsClick: 1678,
    share: 4,
    waAlerts: 4,
  },
  {
    officeName: "Casablanca Tr...",
    city: "Casablanca",
    profileViews: 324,
    phoneCalls: 508,
    gpsClick: 2940,
    share: 8,
    waAlerts: 8,
  },
  {
    officeName: "Moroccan Gol...",
    city: "Essaouira",
    profileViews: 309,
    phoneCalls: 156,
    gpsClick: 3977,
    share: 10,
    waAlerts: 10,
  },
  {
    officeName: "Tanger Curren...",
    city: "Ouarzazate",
    profileViews: 200,
    phoneCalls: 267,
    gpsClick: 2894,
    share: 20,
    waAlerts: 20,
  },
  {
    officeName: "Casablanca Tr...",
    city: "Casablanca",
    profileViews: 324,
    phoneCalls: 508,
    gpsClick: 2940,
    share: 8,
    waAlerts: 8,
  },
  {
    officeName: "Moroccan Gol...",
    city: "Essaouira",
    profileViews: 309,
    phoneCalls: 156,
    gpsClick: 3977,
    share: 10,
    waAlerts: 10,
  },
  {
    officeName: "Tanger Curren...",
    city: "Ouarzazate",
    profileViews: 200,
    phoneCalls: 267,
    gpsClick: 2894,
    share: 20,
    waAlerts: 20,
  },
];

const OverallActivityOriginalData: OverallActivityData[] = [
  {
    officeName: "Atlas Exchange",
    city: "Rabat",
    lastUpdate: "2 hrs ago",
    updates7days: 15,
    activityStatus: "Very Active",
  },
  {
    officeName: "Sahara Forex",
    city: "Marrakech",
    lastUpdate: "5 hrs ago",
    updates7days: 12,
    activityStatus: "Very Active",
  },
  {
    officeName: "DirhamX",
    city: "Tangier",
    lastUpdate: "36 hrs ago",
    updates7days: 3,
    activityStatus: "Active",
  },
  {
    officeName: "Maghreb Cash",
    city: "Agadir",
    lastUpdate: "18 hrs ago",
    updates7days: 1,
    activityStatus: "Low Activity",
  },
  {
    officeName: "RabatX",
    city: "Rabat",
    lastUpdate: "1 hr ago",
    updates7days: 4,
    activityStatus: "Very Active",
  },
  {
    officeName: "Casablanca Trade",
    city: "Casablanca",
    lastUpdate: "3 hrs ago",
    updates7days: 10,
    activityStatus: "Very Active",
  },
  {
    officeName: "Fes Money",
    city: "Fes",
    lastUpdate: "12 hrs ago",
    updates7days: 6,
    activityStatus: "Active",
  },
  {
    officeName: "Oasis Exchange",
    city: "Marrakech",
    lastUpdate: "8 hrs ago",
    updates7days: 9,
    activityStatus: "Very Active",
  },
  {
    officeName: "Tangier Cash",
    city: "Tangier",
    lastUpdate: "24 hrs ago",
    updates7days: 2,
    activityStatus: "Low Activity",
  },
  {
    officeName: "Agadir Forex",
    city: "Agadir",
    lastUpdate: "6 hrs ago",
    updates7days: 5,
    activityStatus: "Active",
  },
  {
    officeName: "Medina Money",
    city: "Casablanca",
    lastUpdate: "4 hrs ago",
    updates7days: 13,
    activityStatus: "Very Active",
  },
  {
    officeName: "Rif Exchange",
    city: "Tetouan",
    lastUpdate: "15 hrs ago",
    updates7days: 7,
    activityStatus: "Active",
  },
  {
    officeName: "Souss Cash",
    city: "Agadir",
    lastUpdate: "48 hrs ago",
    updates7days: 0,
    activityStatus: "Active",
  },
  {
    officeName: "Atlas Forex",
    city: "Rabat",
    lastUpdate: "30 mins ago",
    updates7days: 11,
    activityStatus: "Very Active",
  },
  {
    officeName: "Meknes Trade",
    city: "Meknes",
    lastUpdate: "10 hrs ago",
    updates7days: 8,
    activityStatus: "Active",
  },
];

const Analytics: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>("Overall Activity");
  const [showAboutOfficeFilterModal, setShowAboutOfficeFilterModal] =
    useState(false);
  const [showOfficeEngagementFilterModal, setShowOfficeEngagementFilterModal] =
    useState(false);

  const [aboutOfficeFilters, setAboutOfficeFilters] = useState({
    status: "",
    country: "",
    city: [] as string[],
    dateRange: "",
  });

  const [officeEngagementFilters, setOfficeEngagementFilters] = useState({
    city: [] as string[],
    dateRange: "",
  });

  const [filteredAboutOfficeData, setFilteredAboutOfficeData] = useState<
    AboutOfficeActivityData[]
  >(AboutOfficeOriginalData);
  const [filteredOfficeEngagementData, setFilteredOfficeEngagementData] =
    useState<OfficeEngagementActivityData[]>(OfficeEngagementOriginalData);
  const [filteredOverallActivityData, setFilteredOverallActivityData] =
    useState<OverallActivityData[]>(OverallActivityOriginalData);

  const availableCities = Array.from(
    new Set(AboutOfficeOriginalData.map((data) => data.city))
  );

  useEffect(() => {
    const newFilteredData = filterAboutOfficeData(
      AboutOfficeOriginalData,
      aboutOfficeFilters
    );
    setFilteredAboutOfficeData(newFilteredData);
  }, [aboutOfficeFilters]);

  useEffect(() => {
    const newFilteredData = filterOfficeEngagementData(
      OfficeEngagementOriginalData,
      officeEngagementFilters
    );
    setFilteredOfficeEngagementData(newFilteredData);
  }, [officeEngagementFilters]);

  const historyOptions: HistoryOption[] = [
    { value: "last-7-days", label: "Last 7 days" },
    { value: "last-30-days", label: "Last 30 days" },
    { value: "last-90-days", label: "Last 90 days" },
  ];

  const handleHistorySelect = (option: HistoryOption): void => {
    console.log("Selected:", option);
  };

  const tabs: TabType[] = [
    { id: "Overall Activity", label: "Overall Activity" },
    { id: "Office Engagement", label: "Office Engagement" },
    { id: "About Exchange Office", label: "About Exchange Office" },
  ];

  const handleTabClick = (tabId: string): void => {
    setActiveTab(tabId);
    setShowAboutOfficeFilterModal(false);
    setShowOfficeEngagementFilterModal(false);
  };

  const handleFilterButtonClick = () => {
    if (activeTab === "About Exchange Office") {
      setShowAboutOfficeFilterModal(true);
    } else if (activeTab === "Office Engagement") {
      setShowOfficeEngagementFilterModal(true);
    }
  };

  const filterAboutOfficeData = (
    data: AboutOfficeActivityData[],
    filters: typeof aboutOfficeFilters
  ): AboutOfficeActivityData[] => {
    let filtered = data;

    if (filters.status) {
      filtered = filtered.filter((item) => item.status === filters.status);
    }

    if (filters.country) {
      filtered = filtered.filter(
        (item) => item.country.toLowerCase() === filters.country.toLowerCase()
      );
    }

    if (filters.city && filters.city.length > 0) {
      filtered = filtered.filter((item) =>
        filters.city.some(
          (city) => item.city.toLowerCase() === city.toLowerCase()
        )
      );
    }

    if (
      filters.dateRange &&
      filters.dateRange !== "" &&
      filters.dateRange !== "alltime"
    ) {
      const today = new Date();
      let startDate = new Date();

      switch (filters.dateRange) {
        case "last7days":
          startDate.setDate(today.getDate() - 7);
          break;
        case "last1month":
          startDate.setMonth(today.getMonth() - 1);
          break;
        case "last6month":
          startDate.setMonth(today.getMonth() - 6);
          break;
        case "last1year":
          startDate.setFullYear(today.getFullYear() - 1);
          break;
      }

      startDate.setHours(0, 0, 0, 0);

      filtered = filtered.filter((item) => {
        const registrationDate = new Date(item.registrationDate);
        registrationDate.setHours(0, 0, 0, 0);
        return registrationDate >= startDate;
      });
    }

    return filtered;
  };

  const filterOfficeEngagementData = (
    data: OfficeEngagementActivityData[],
    filters: typeof officeEngagementFilters
  ): OfficeEngagementActivityData[] => {
    return data.filter((item) => {
      if (
        filters.city &&
        filters.city.length > 0 &&
        !filters.city.includes(item.city)
      ) {
        return false;
      }
      return true;
    });
  };

  const handleCloseAboutOfficeModal = () => {
    setShowAboutOfficeFilterModal(false);
  };

  const handleAboutOfficeFilterChange = (
    filterType: keyof typeof aboutOfficeFilters,
    value: string | string[]
  ) => {
    setAboutOfficeFilters((prev) => ({
      ...prev,
      [filterType]: value,
    }));
  };

  const handleApplyAboutOfficeFilters = () => {
    console.log("Applying About Exchange Office filters:", aboutOfficeFilters);
    setShowAboutOfficeFilterModal(false);
  };

  const handleClearAboutOfficeFilters = () => {
    setAboutOfficeFilters({
      status: "",
      country: "",
      city: [],
      dateRange: "",
    });
    setFilteredAboutOfficeData(AboutOfficeOriginalData);
    setShowAboutOfficeFilterModal(false);
  };

  const handleCloseOfficeEngagementModal = () => {
    setShowOfficeEngagementFilterModal(false);
  };

  const handleOfficeEngagementFilterChange = (
    filterType: keyof typeof officeEngagementFilters,
    value: string | string[]
  ) => {
    setOfficeEngagementFilters((prev) => ({
      ...prev,
      [filterType]: value,
    }));
  };

  const handleApplyOfficeEngagementFilters = () => {
    console.log("Applying Office Engagement filters:", officeEngagementFilters);
    setShowOfficeEngagementFilterModal(false);
  };

  const handleClearOfficeEngagementFilters = () => {
    setOfficeEngagementFilters({
      city: [],
      dateRange: "",
    });
    setFilteredOfficeEngagementData(OfficeEngagementOriginalData);
    setShowOfficeEngagementFilterModal(false);
  };

  const renderTabContent = (): JSX.Element => {
    switch (activeTab) {
      case "Overall Activity":
        return (
          <TableWrapper
            data={filteredOverallActivityData}
            setData={setFilteredOverallActivityData}
            sortableColumns={[
              { key: "officeName", label: "Office Name" },
              { key: "city", label: "City" },
              { key: "lastUpdate", label: "Last Update" },
              { key: "updates7days", label: "Updates (7 days)" },
              { key: "activityStatus", label: "Activity Status" },
            ]}
            filterOptions={[
              {
                key: "activityStatus",
                label: "Activity Status",
                options: ["Very Active", "Active", "Low Activity"],
              },
              {
                key: "city",
                label: "City",
                options: Array.from(
                  new Set(OverallActivityOriginalData.map((data) => data.city))
                ),
              },
            ]}
          >
            <OverallActivity data={filteredOverallActivityData} />
          </TableWrapper>
        );
      case "About Exchange Office":
        return (
          <TableWrapper
            data={filteredAboutOfficeData}
            setData={setFilteredAboutOfficeData}
            sortableColumns={[
              { key: "officeName", label: "Office Name" },
              { key: "city", label: "City" },
              { key: "country", label: "Country" },
              { key: "registrationDate", label: "Registration Date" },
              { key: "status", label: "Status" },
              { key: "duration", label: "Duration" },
              { key: "registeredCount", label: "Registered Count" },
            ]}
            filterOptions={[
              {
                key: "status",
                label: "Status",
                options: ["Validated", "Pending", "Rejected"],
              },
              {
                key: "country",
                label: "Country",
                options: Array.from(
                  new Set(AboutOfficeOriginalData.map((data) => data.country))
                ),
              },
            ]}
          >
            <AboutExchangeOffice data={filteredAboutOfficeData} />
          </TableWrapper>
        );
      case "Office Engagement":
        return (
          <TableWrapper
            data={filteredOfficeEngagementData}
            setData={setFilteredOfficeEngagementData}
            sortableColumns={[
              { key: "officeName", label: "Office Name" },
              { key: "city", label: "City" },
              { key: "profileViews", label: "Profile Views" },
              { key: "phoneCalls", label: "Phone Calls" },
              { key: "gpsClick", label: "GPS Click" },
              { key: "share", label: "Share" },
              { key: "waAlerts", label: "WA Alerts" },
            ]}
            filterOptions={[
              {
                key: "city",
                label: "City",
                options: availableCities,
              },
            ]}
          >
            <OfficeEngagement data={filteredOfficeEngagementData} />
          </TableWrapper>
        );
      default:
        return (
          <div className="">
            <h1>Select a tab</h1>
          </div>
        );
    }
  };

  return (
    <AdminLayout>
      <div>
        <div className="flex items-start gap-6 justify-between md:flex-row flex-col">
          <div className="max-w-[485px]">
            <h1 className="text-[#111111] text-[26px] md:text-[32px] leading-[31px] md:leading-[38px] font-bold mb-2">
              User Engagement Analysis
            </h1>
            <p className="text-[#585858] text-[14px] leading-[20px] font-normal">
              Track user interactions, behavior patterns, and retention metrics
              to better understand engagement and drive product improvements.
            </p>
          </div>
          <HistoryDropdown
            options={historyOptions}
            defaultValue="Last 7 days"
            onSelect={handleHistorySelect}
          />
        </div>

        <div className="py-8 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2.5">
          <StatsCard
            title="Profile Review"
            value="1520"
            percentage="108"
            changeText="+12% vs last week"
            isPositive={true}
          />
          <StatsCard
            title="Phone Calls"
            value="289"
            percentage="26"
            changeText="+8% vs last week"
            isPositive={true}
          />
          <StatsCard
            title="GPS Clicked"
            value="430"
            percentage="32"
            changeText="+6% vs last week"
            isPositive={true}
          />
          <StatsCard
            title="WA Alert Price"
            value="215"
            percentage="5"
            changeText="+15% vs last week"
            isPositive={true}
          />
          <StatsCard
            title="Agency"
            value="82"
            percentage="2"
            changeText="+4% vs last week"
            isPositive={true}
          />
        </div>

        <div className="">
          <div className="md:border-b border-[#DEDEDE] mb-6 sm:mb-8 overflow-hidden relative">
            <div
              className="flex overflow-x-auto items-center justify-between hide-scrollbar"
              style={{
                background:
                  "linear-gradient(270deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%)",
              }}
            >
              <div className="flex items-center hide-scrollbar pr-[50px]">
                {tabs.map((tab: TabType) => (
                  <button
                    key={tab.id}
                    onClick={() => handleTabClick(tab.id)}
                    className={`flex-shrink-0 px-0 pb-[8px] cursor-pointer pt-[8.11px] mr-6 last:mr-0 text-[14.19px] font-medium leading-[18px] transition-colors whitespace-nowrap ${
                      activeTab === tab.id
                        ? "text-[#111111] border-b-2 border-[#20523C]"
                        : "text-[#585858] border-transparent hover:text-[#585858]"
                    }`}
                    type="button"
                    aria-selected={activeTab === tab.id}
                    role="tab"
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
              {(activeTab === "About Exchange Office" ||
                activeTab === "Office Engagement") && (
                <button
                  className="absolute -right-1 md:right-0 bottom-0 p-2.5 text-nowrap md:w-[94px] h-[37px] flex items-center gap-1 cursor-pointer bg-white"
                  onClick={handleFilterButtonClick}
                >
                  <div
                    className="absolute inset-y-0 -left-6 w-[20px] pointer-events-none sm:hidden block"
                    style={{
                      background:
                        "linear-gradient(to left, rgba(0, 0, 0, 0.20) 0%, rgba(0,0,0,0) 100%)",
                      zIndex: 10,
                    }}
                  />
                  <Image
                    src="/assets/filter.svg"
                    alt="filter"
                    width={14}
                    height={14}
                    className="md:w-[14px] md:h-[14px] w-[20px] md:-mt-0.5"
                  />
                  <h3 className="text-[#20523C] text-[14px] font-medium leading-[18px] md:block hidden">
                    Filter by
                  </h3>
                </button>
              )}
            </div>
          </div>

          <div role="tabpanel" aria-labelledby={`tab-${activeTab}`}>
            {renderTabContent()}
          </div>
        </div>
      </div>

      <AboutOfficeFilterModal
        show={showAboutOfficeFilterModal}
        onClose={handleCloseAboutOfficeModal}
        filters={aboutOfficeFilters}
        onFilterChange={handleAboutOfficeFilterChange}
        onApply={handleApplyAboutOfficeFilters}
        onClear={handleClearAboutOfficeFilters}
        availableCities={availableCities}
      />

      <OfficeEngagementFilterModal
        show={showOfficeEngagementFilterModal}
        onClose={handleCloseOfficeEngagementModal}
        filters={officeEngagementFilters}
        onFilterChange={handleOfficeEngagementFilterChange}
        onApply={handleApplyOfficeEngagementFilters}
        onClear={handleClearOfficeEngagementFilters}
        availableCities={availableCities}
      />
    </AdminLayout>
  );
};

export default Analytics;
