"use client";
import React, { useState } from "react";
import AdminLayout from "@/components/AdminComponents/AdminLayout";
import HistoryDropdown from "@/components/ExchangeLeadboard/HistoryDropdown";
import Image from "next/image";

interface StatsCardProps {
  title: string;
  value: string | number;
  percentage: string | number;
  changeText: string;
  isPositive?: boolean;
}

interface UpdateData {
  exchangeOffice: string;
  alerts: number;
  views: number;
  updateRate: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  percentage,
  changeText,
  isPositive = true,
}) => {
  return (
     <div
      className="rounded-lg transition-all group duration-300 ease-out hover:scale-105 hover:-translate-y-2 cursor-pointer"
      style={{
        boxShadow: "0px 4px 14px rgba(0, 0, 0, 0.10), 0px 4px 4px rgba(0, 0, 0, 0.08)",
        transition: 'all 0.3s ease'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = '0px 8px 10px rgba(0, 0, 0, 0.15), 0px 4px 8px rgba(0, 0, 0, 0.12)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = "0px 4px 14px rgba(0, 0, 0, 0.10), 0px 4px 4px rgba(0, 0, 0, 0.08)";
      }}
    >
      <div className='p-4'>
        <div className="flex items-center gap-1">
          <h2 className="text-[#585858] text-[14px] leading-[20px] font-normal">
            {title}
          </h2>
          <Image src="/assets/info.svg" alt="info" width={14} height={14} />
        </div>
        <div className="mt-4 flex items-center gap-1.5">
          <h2 className="text-[#111111] text-[42px] leading-[50px] font-semibold">
            {value}
          </h2>
          <div
            className={`mt-2.5 ${
              isPositive ? "bg-[#C2ECD2]" : "bg-[#FECACA]"
            } rounded-[1000px] px-1.5 py-0.5 flex justify-center gap-0.5`}
          >
            <Image
              src={
                isPositive ? "/assets/arrow-up.svg" : "/assets/arrow-down.svg"
              }
              alt={isPositive ? "arrow-up" : "arrow-down"}
              width={14.19}
              height={14.19}
            />
            <h3
              className={`${
                isPositive ? "text-[#20523C]" : "text-[#991B1B]"
              } text-[12px] leading-[17px] font-normal mt-[1px]`}
            >
              {percentage}%
            </h3>
          </div>
        </div>
      </div>
      <div className="bg-[#F5F7F9] py-3 px-4 rounded-b-lg group-hover:bg-[#EDF2F7] transition-colors duration-300">
        <h2 className="text-[#585858] text-[14px] font-normal leading-[20px]">
          {changeText}
        </h2>
      </div>
    </div>
  );
};

const Dashboard: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState<{
    key: keyof UpdateData | null;
    direction: "asc" | "desc" | null;
  }>({ key: null, direction: null });

  const historyOptions = [
    { value: "last-7-days", label: "Last 7 days" },
    { value: "last-30-days", label: "Last 30 days" },
    { value: "last-90-days", label: "Last 90 days" },
  ];

  const updatesData: UpdateData[] = [
    {
      exchangeOffice: "Atlas Exchange",
      alerts: 2,
      views: 142,
      updateRate: "4.4h",
    },
    {
      exchangeOffice: "Altayeb",
      alerts: 0,
      views: 53,
      updateRate: "16.0h",
    },
    {
      exchangeOffice: "Apex Innovations",
      alerts: 5,
      views: 72,
      updateRate: "8.3h",
    },
    {
      exchangeOffice: "Archetype Solutions",
      alerts: 12,
      views: 207,
      updateRate: "2.2h",
    },
    {
      exchangeOffice: "Aether Technologies",
      alerts: 3,
      views: 189,
      updateRate: "1.2h",
    },
    {
      exchangeOffice: "Archetype Solutions",
      alerts: 12,
      views: 207,
      updateRate: "2.2h",
    },
    {
      exchangeOffice: "Aether Technologies",
      alerts: 3,
      views: 189,
      updateRate: "1.2h",
    },
    {
      exchangeOffice: "Atlas Exchange",
      alerts: 2,
      views: 142,
      updateRate: "4.4h",
    },
    {
      exchangeOffice: "Altayeb",
      alerts: 0,
      views: 53,
      updateRate: "16.0h",
    },
    {
      exchangeOffice: "Apex Innovations",
      alerts: 5,
      views: 72,
      updateRate: "8.3h",
    },
    {
      exchangeOffice: "Archetype Solutions",
      alerts: 12,
      views: 207,
      updateRate: "2.2h",
    },
    {
      exchangeOffice: "Aether Technologies",
      alerts: 3,
      views: 189,
      updateRate: "1.2h",
    },
    {
      exchangeOffice: "Archetype Solutions",
      alerts: 12,
      views: 207,
      updateRate: "2.2h",
    },
    {
      exchangeOffice: "Aether Technologies",
      alerts: 3,
      views: 189,
      updateRate: "1.2h",
    },
  ];

  const filteredData = updatesData.filter((item) =>
    item.exchangeOffice.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortConfig.key || !sortConfig.direction) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    if (typeof aValue === "string" && typeof bValue === "string") {
      if (sortConfig.key === "updateRate") {
        const aNum = parseFloat(aValue.replace("h", ""));
        const bNum = parseFloat(bValue.replace("h", ""));
        return sortConfig.direction === "asc" ? aNum - bNum : bNum - aNum;
      }
      return sortConfig.direction === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    if (typeof aValue === "number" && typeof bValue === "number") {
      return sortConfig.direction === "asc" ? aValue - bValue : bValue - aValue;
    }

    return 0;
  });

  const totalPages = Math.ceil(sortedData.length / rowsPerPage);
  const paginatedData = sortedData.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage
  );

  const handleSort = (key: keyof UpdateData) => {
    setSortConfig((prevConfig) => ({
      key,
      direction:
        prevConfig.key === key && prevConfig.direction === "asc"
          ? "desc"
          : "asc",
    }));
  };

  return (
    <AdminLayout>
      <div>
        <div className="flex items-start gap-6 justify-between md:flex-row flex-col">
          <div className="max-w-[485px]">
            <h1 className="text-[#111111] text-[26px] md:text-[32px] leading-[31px] md:leading-[38px] font-bold mb-2">
              Dashboard
            </h1>
            <p className="text-[#585858] text-[14px] leading-[20px] font-normal">
              Central hub for managing exchange office data, submissions, and
              updates across cities and countries.
            </p>
          </div>
          <HistoryDropdown
            options={historyOptions}
            defaultValue="Last 7 days"
            onSelect={(option) => console.log("Selected:", option)}
          />
        </div>
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <StatsCard
            title="Total Office"
            value="435"
            percentage="18"
            changeText="+9 from last week"
            isPositive={true}
          />
          <StatsCard
            title="Update This week"
            value="81"
            percentage="6"
            changeText="+2 from last week"
            isPositive={true}
          />
          <StatsCard
            title="Alerts"
            value="5"
            percentage="32"
            changeText="+8 from last last week"
            isPositive={true}
          />
        </div>

        <div className="mt-6 md:mt-8">
          <h2 className="text-[#111111] text-[20px] leading-[24px] font-bold mb-4">
            Updates Today
          </h2>

          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
            <div className="relative w-full md:w-[300px]">
              <input
                type="text"
                placeholder="Search exchange office..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-[#DEDEDE] rounded-lg pl-10 focus:outline-none focus:border-[#3BEE5C]"
              />
              <Image
                src="/assets/search-normal.svg"
                alt="search"
                width={20}
                height={20}
                className="absolute left-3 top-1/2 transform -translate-y-1/2"
              />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-[14px] text-[#585858]">Rows per page:</span>
              <select
                value={rowsPerPage}
                onChange={(e) => {
                  setRowsPerPage(Number(e.target.value));
                  setCurrentPage(1);  
                }}
                className="border border-[#DEDEDE] rounded-lg px-2 py-1 focus:outline-none focus:border-[#3BEE5C]"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>

          {/* Desktop Table View */}
          <div className="hidden md:block bg-white rounded-lg text-nowrap border border-[#DEDEDE]">
            <div className="grid grid-cols-4 gap-4 bg-[#F3F4F8] px-[16.22px] py-3.5 border-b border-[#DEDEDE]">
              <button
                onClick={() => handleSort("exchangeOffice")}
                className="flex items-center gap-1 text-[#111111] text-[14px] font-bold leading-[17px] cursor-pointer"
              >
                <span>Exchange Office</span>
                <Image
                  src="/assets/sort-table.svg"
                  alt="sort"
                  width={14.39}
                  height={14.39}
                  className={`transform ${
                    sortConfig.key === "exchangeOffice"
                      ? sortConfig.direction === "asc"
                        ? "rotate-180"
                        : "rotate-0"
                      : ""
                  }`}
                />
              </button>
              <button
                onClick={() => handleSort("alerts")}
                className="flex items-center gap-1 text-[#111111] text-[14px] font-bold leading-[17px] cursor-pointer"
              >
                <span>Alerts (7 days)</span>
                <Image
                  src="/assets/sort-table.svg"
                  alt="sort"
                  width={14.39}
                  height={14.39}
                  className={`transform ${
                    sortConfig.key === "alerts"
                      ? sortConfig.direction === "asc"
                        ? "rotate-180"
                        : "rotate-0"
                      : ""
                  }`}
                />
              </button>
              <button
                onClick={() => handleSort("views")}
                className="flex items-center gap-1 text-[#111111] text-[14px] font-bold leading-[17px] cursor-pointer"
              >
                <span>Views (7 days)</span>
                <Image
                  src="/assets/sort-table.svg"
                  alt="sort"
                  width={14.39}
                  height={14.39}
                  className={`transform ${
                    sortConfig.key === "views"
                      ? sortConfig.direction === "asc"
                        ? "rotate-180"
                        : "rotate-0"
                      : ""
                  }`}
                />
              </button>
              <button
                onClick={() => handleSort("updateRate")}
                className="flex items-center gap-1 text-[#111111] text-[14px] font-bold leading-[17px] cursor-pointer"
              >
                <span>Update Rate</span>
                <Image
                  src="/assets/sort-table.svg"
                  alt="sort"
                  width={14.39}
                  height={14.39}
                  className={`transform ${
                    sortConfig.key === "updateRate"
                      ? sortConfig.direction === "asc"
                        ? "rotate-180"
                        : "rotate-0"
                      : ""
                  }`}
                />
              </button>
            </div>

            <div>
              {paginatedData.map((item, index) => (
                <div
                  key={index}
                  className="grid grid-cols-4 gap-4 px-[16.22px] py-3.5 border-b border-[#DEDEDE] last:border-b-0 hover:bg-gray-50 cursor-pointer transition duration-150"
                >
                  <div className="text-[#585858] text-[16.22px] leading-[19px] font-normal truncate">
                    {item.exchangeOffice}
                  </div>
                  <div className="text-[#585858] text-[16.22px] leading-[19px] font-normal">
                    {item.alerts}
                  </div>
                  <div className="text-[#585858] text-[16.22px] leading-[19px] font-normal">
                    {item.views}
                  </div>
                  <div className="text-[#585858] text-[16.22px] leading-[19px] font-normal">
                    {item.updateRate}
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination Controls */}
            {sortedData.length > 0 && (
              <div className="flex justify-between items-center mt-4 px-4">
                <div className="text-[14px] text-[#585858]">
                  Showing {(currentPage - 1) * rowsPerPage + 1} to{" "}
                  {Math.min(currentPage * rowsPerPage, sortedData.length)} of{" "}
                  {sortedData.length} entries
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={currentPage === 1}
                    className={`px-3 py-1 rounded cursor-pointer border ${
                      currentPage === 1
                        ? "border-[#DEDEDE] text-[#DEDEDE] cursor-not-allowed"
                        : "border-[#3BEE5C] text-[#3BEE5C] hover:bg-[#3BEE5C] hover:text-white"
                    }`}
                  >
                    Previous
                  </button>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                    (page) => (
                      <button
                        key={page}
                        onClick={() => setCurrentPage(page)}
                        className={`w-8 h-8 rounded cursor-pointer ${
                          currentPage === page
                            ? "bg-[#3BEE5C] text-white"
                            : "border border-[#DEDEDE] text-[#585858] hover:border-[#3BEE5C]"
                        }`}
                      >
                        {page}
                      </button>
                    )
                  )}
                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                    }
                    disabled={currentPage === totalPages}
                    className={`px-3 py-1 rounded cursor-pointer border ${
                      currentPage === totalPages
                        ? "border-[#DEDEDE] text-[#DEDEDE] cursor-not-allowed"
                        : "border-[#3BEE5C] text-[#3BEE5C] hover:bg-[#3BEE5C] hover:text-white"
                    }`}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Mobile Card View */}
          <div className="md:hidden grid grid-cols-1 sm:grid-cols-2 gap-2.5">
            {paginatedData.map((item, index) => (
              <div
                key={index}
                className="p-4 w-full rounded-lg border border-[#DEDEDE]"
              >
                <div className="space-y-2.5">
                  <div className="flex items-center gap-2.5">
                    <div className="w-[114px] flex items-center gap-1 justify-between">
                      <h3 className="text-[14px] font-medium leading-[17px] text-[#111111]">
                        Exchange Office
                      </h3>
                      <h3 className="text-[14px] font-medium leading-[17px] text-[#111111]">
                        :
                      </h3>
                    </div>
                    <h2 className="text-[14px] font-normal leading-[17px] text-[#585858]">
                      {item.exchangeOffice}
                    </h2>
                  </div>
                  <div className="flex items-center gap-2.5">
                    <div className="w-[114px] flex items-center gap-1 justify-between">
                      <h3 className="text-[14px] font-medium leading-[17px] text-[#111111]">
                        Alerts (7 days)
                      </h3>
                      <h3 className="text-[14px] font-medium leading-[17px] text-[#111111]">
                        :
                      </h3>
                    </div>
                    <h2 className="text-[14px] font-normal leading-[17px] text-[#585858]">
                      {item.alerts}
                    </h2>
                  </div>
                  <div className="flex items-center gap-2.5">
                    <div className="w-[114px] flex items-center gap-1 justify-between">
                      <h3 className="text-[14px] font-medium leading-[17px] text-[#111111]">
                        Views (7 days)
                      </h3>
                      <h3 className="text-[14px] font-medium leading-[17px] text-[#111111]">
                        :
                      </h3>
                    </div>
                    <h2 className="text-[14px] font-normal leading-[17px] text-[#585858]">
                      {item.views}
                    </h2>
                  </div>
                  <div className="flex items-center gap-2.5">
                    <div className="w-[114px] flex items-center gap-1 justify-between">
                      <h3 className="text-[14px] font-medium leading-[17px] text-[#111111]">
                        Update Rate
                      </h3>
                      <h3 className="text-[14px] font-medium leading-[17px] text-[#111111]">
                        :
                      </h3>
                    </div>
                    <h2 className="text-[14px] font-normal leading-[17px] text-[#585858]">
                      {item.updateRate}
                    </h2>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Dashboard;
