"use client";
import AdminLayout from "@/components/AdminComponents/AdminLayout";
import ThreeDots from "@/components/SvgIcons/ThreeDots";
import React, { useState, useEffect, useRef } from "react";
import RejectThisRequest from "@/components/AdminComponents/RegisterRequest/RejectThisRequest";
import RejectSuccessModal from "@/components/AdminComponents/RegisterRequest/RejectSuccessModal";
import ApproveModal from "@/components/AdminComponents/RegisterRequest/ApproveModal";
import ApproveSuccessModal from "@/components/AdminComponents/RegisterRequest/ApproveSuccessModal";
import Image from "next/image";

interface Exchange {
  id: number;
  name: string;
  address: string;
  status: "Requested" | "On Hold" | "Reject" | "Approved";
}

const RegisterRequest: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const [searchTerm, setSearchTerm] = useState("");

  const [sortConfig, setSortConfig] = useState<{
    key: keyof Exchange | null;
    direction: "asc" | "desc" | null;
  }>({ key: null, direction: null });

  const [exchanges, setExchanges] = useState<Exchange[]>([
    {
      id: 1,
      name: "Atlas Exchange",
      address: "4140 Parker Rd. Allentown, Mexico",
      status: "Requested",
    },
    {
      id: 2,
      name: "Sahara Exchange",
      address: "6391 Elgin St. Celina, Delaware 10299",
      status: "On Hold",
    },
    {
      id: 3,
      name: "DrhamX",
      address: "1901 Thornridge Cir, Hawaii 81063",
      status: "Reject",
    },
    {
      id: 4,
      name: "Pacific Trade",
      address: "7823 Ocean Dr. San Diego, California 92101",
      status: "Approved",
    },
    {
      id: 5,
      name: "Nexus Exchange",
      address: "1452 Maple Ave. Toronto, Canada 66784",
      status: "Approved",
    },
    {
      id: 6,
      name: "GlobalX",
      address: "301 Pine St. Seattle, Washington 98101",
      status: "Requested",
    },
    {
      id: 7,
      name: "Horizon Market",
      address: "8923 Cedar Rd. Austin, Texas 78701",
      status: "On Hold",
    },
    {
      id: 8,
      name: "Vertex Exchange",
      address: "4567 Birch Ln. Miami, Florida 33133",
      status: "Approved",
    },
    {
      id: 9,
      name: "Equinox Trade",
      address: "1234 Elm St. Chicago, Illinois 60610",
      status: "Reject",
    },
    {
      id: 10,
      name: "Aurora Exchange",
      address: "7890 Oak Ave. Vancouver, Canada 99876",
      status: "Approved",
    },
    {
      id: 11,
      name: "Starlight Market",
      address: "3210 Willow Dr. Denver, Colorado 80202",
      status: "Requested",
    },
    {
      id: 12,
      name: "Lunar Exchange",
      address: "6543 Spruce St. Portland, Oregon 97209",
      status: "On Hold",
    },
    {
      id: 13,
      name: "Solar Trade",
      address: "9876 Magnolia Rd. Atlanta, Georgia 30303",
      status: "Approved",
    },
    {
      id: 14,
      name: "Orbit Exchange",
      address: "2345 Laurel Ln. Boston, Massachusetts 02108",
      status: "Reject",
    },
    {
      id: 15,
      name: "Cosmo Market",
      address: "5678 Chestnut Ave. Phoenix, Arizona 85004",
      status: "Approved",
    },
  ]);

  const [openDropdown, setOpenDropdown] = useState<number | null>(null);
  const [showMobileModal, setShowMobileModal] = useState<number | null>(null);
  const [rejectingExchangeId, setRejectingExchangeId] = useState<number | null>(
    null
  );
  const [successExchangeId, setSuccessExchangeId] = useState<number | null>(
    null
  );
  const [rejectLoading, setRejectLoading] = useState(false);
  const [approveModalOpen, setApproveModalOpen] = useState(false);
  const [selectedExchange, setSelectedExchange] = useState<Exchange | null>(
    null
  );
  const [showRejectFromApprove, setShowRejectFromApprove] = useState(false);
  const [approveSuccessModalOpen, setApproveSuccessModalOpen] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);

  const filteredData = exchanges.filter(
    (item) =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortConfig.key || !sortConfig.direction) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    if (sortConfig.key === "status") {
      const statusOrder: Record<Exchange["status"], number> = {
        Approved: 1,
        "On Hold": 2,
        Requested: 3,
        Reject: 4,
      };
      return sortConfig.direction === "asc"
        ? statusOrder[aValue as Exchange["status"]] -
            statusOrder[bValue as Exchange["status"]]
        : statusOrder[bValue as Exchange["status"]] -
            statusOrder[aValue as Exchange["status"]];
    }

    return sortConfig.direction === "asc"
      ? String(aValue).localeCompare(String(bValue))
      : String(bValue).localeCompare(String(aValue));
  });

  const totalPages = Math.ceil(sortedData.length / rowsPerPage);
  const paginatedData = sortedData.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage
  );

  const handleSort = (key: keyof Exchange) => {
    setSortConfig((prevConfig) => ({
      key,
      direction:
        prevConfig.key === key && prevConfig.direction === "asc"
          ? "desc"
          : "asc",
    }));
  };

  const closeAllModals = () => {
    setOpenDropdown(null);
    setShowMobileModal(null);
    setRejectingExchangeId(null);
    setSuccessExchangeId(null);
    setApproveModalOpen(false);
    setSelectedExchange(null);
    setShowRejectFromApprove(false);
    setApproveSuccessModalOpen(false);
  };

  const handleApprove = (id: number): void => {
    setExchanges(
      exchanges.map((exchange) =>
        exchange.id === id
          ? { ...exchange, status: "Approved" as const }
          : exchange
      )
    );
    closeAllModals();
  };

  const handleHold = (id: number): void => {
    setExchanges((prev) =>
      prev.map((exchange) =>
        exchange.id === id
          ? { ...exchange, status: "On Hold" as const }
          : exchange
      )
    );
    closeAllModals();
  };

  const handleReject = (id: number): void => {
    setExchanges(
      exchanges.map((exchange) =>
        exchange.id === id
          ? { ...exchange, status: "Reject" as const }
          : exchange
      )
    );
    setOpenDropdown(null);
    setShowMobileModal(null);
  };

  const toggleDropdown = (id: number): void => {
    setOpenDropdown(openDropdown === id ? null : id);
  };

  const openMobileModal = (id: number): void => {
    if (successExchangeId !== null) return;
    closeAllModals();
    setShowMobileModal(id);
  };

  const closeMobileModal = (): void => {
    setShowMobileModal(null);
  };

  const openRejectModal = (id: number) => {
    if (successExchangeId !== null) return;
    closeAllModals();
    setRejectingExchangeId(id);
  };

  const closeRejectModal = () => {
    setRejectingExchangeId(null);
  };

  const handleRejectSubmit = async (
    exchangeId: number,
    reason: string,
    message: string
  ) => {
    setRejectLoading(true);

    setRejectingExchangeId(null);
    setShowRejectFromApprove(false);

    setTimeout(() => {
      setExchanges(
        exchanges.map((exchange) =>
          exchange.id === exchangeId
            ? { ...exchange, status: "Reject" as const }
            : exchange
        )
      );
      setRejectLoading(false);
      setSuccessExchangeId(exchangeId);
    }, 500);
  };

  const handleCloseSuccessModal = () => {
    setSuccessExchangeId(null);
    closeAllModals();
  };

  const handleCloseApproveSuccessModal = () => {
    setApproveSuccessModalOpen(false);
  };

  const handleOpenApproveModal = (exchange: Exchange) => {
    closeAllModals();
    setSelectedExchange(exchange);
    setApproveModalOpen(true);
  };

  const handleApproveFromModal = () => {
    if (selectedExchange) {
      handleApprove(selectedExchange.id);
      setApproveSuccessModalOpen(true);
    }
  };

  useEffect(() => {
    if (successExchangeId !== null) {
      setShowMobileModal(null);
    }
  }, [successExchangeId]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showMobileModal !== null) {
        const target = event.target as HTMLElement;
        if (target.classList.contains("modal-backdrop")) {
          closeMobileModal();
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showMobileModal]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdown !== null) {
        const dropdown = dropdownRef.current;
        const target = event.target as Node;
        if (dropdown && !dropdown.contains(target)) {
          setOpenDropdown(null);
        }
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setOpenDropdown(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleEscape);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [openDropdown]);

  return (
    <AdminLayout>
      <div>
        <h1 className="text-[#111111] text-[26px] md:text-[32px] leading-[31px] md:leading-[38px] font-bold mb-2">
          Exchange Registration Request
        </h1>
        <p className="text-[#585858] text-[14px] leading-[20px] font-normal">
          Review and validate new exchange office registrations submitted to the
          platform.
        </p>

        <div className="mt-6 lg:mt-8">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-4">
            <div className="relative w-full lg:w-[300px]">
              <input
                type="text"
                placeholder="Search by name or address..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1);  
                }}
                className="w-full px-4 py-2 border border-[#DEDEDE] rounded-lg pl-10 focus:outline-none focus:border-[#3BEE5C]"
              />
              <Image
                src="/assets/search-normal.svg"
                alt="search"
                width={20}
                height={20}
                className="absolute left-3 top-1/2 transform -translate-y-1/2"
              />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-[14px] text-[#585858]">Rows per page:</span>
              <select
                value={rowsPerPage}
                onChange={(e) => {
                  setRowsPerPage(Number(e.target.value));
                  setCurrentPage(1);  
                }}
                className="border border-[#DEDEDE] rounded-lg px-2 py-1 focus:outline-none focus:border-[#3BEE5C]"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>

          {/* Desktop Table View */}
          <div className="hidden lg:block">
            <div className="bg-white text-nowrap">
              <div className="grid grid-cols-13 gap-3 lg:gap-0 py-3 pr-4">
                <button
                  onClick={() => handleSort("name")}
                  className="col-span-3  pl-6 flex items-center gap-1 text-[14px] leading-[20px] font-medium text-[#111111] cursor-pointer"
                >
                  <span>Name</span>
                  <Image
                    src="/assets/sort-table.svg"
                    alt="sort"
                    width={14.39}
                    height={14.39}
                    className={`transform ${
                      sortConfig.key === "name"
                        ? sortConfig.direction === "asc"
                          ? "rotate-180"
                          : "rotate-0"
                        : ""
                    }`}
                  />
                </button>
                <div className="col-span-5">
                  <span className="text-[14px] leading-[20px] font-medium text-[#111111]">
                    Address
                  </span>
                </div>
                <button
                  onClick={() => handleSort("status")}
                  className="col-span-2 flex items-center gap-1 text-[14px] leading-[20px] font-medium text-[#111111] cursor-pointer"
                >
                  <span>Status</span>
                  <Image
                    src="/assets/sort-table.svg"
                    alt="sort"
                    width={14.39}
                    height={14.39}
                    className={`transform ${
                      sortConfig.key === "status"
                        ? sortConfig.direction === "asc"
                          ? "rotate-180"
                          : "rotate-0"
                        : ""
                    }`}
                  />
                </button>
                <div className="col-span-3"></div>
              </div>
              <div className="space-y-2">
                {paginatedData.map((exchange, index) => (
                  <div
                    key={exchange.id}
                    className={`grid grid-cols-13 gap-3 lg:gap-0 items-center py-3 pr-3 border border-[#DEDEDE] rounded-[12px]`}
                  >
                    <div className="col-span-3 truncate pl-6">
                      <span className="text-[14px] leading-[20px] font-normal text-[#111111] truncate">
                        {exchange.name}
                      </span>
                    </div>
                    <div className="col-span-5 flex items-start justify-start">
                      <span className="text-[14px] font-normal leading-[20px] text-[#111111] truncate">
                        {exchange.address}
                      </span>
                    </div>
                    <div className="col-span-2">
                      <span className="text-[14px] leading-[20px] font-normal text-[#111111]">
                        {exchange.status}
                      </span>
                    </div>
                    <div className="col-span-3 flex items-center justify-end gap-4">
                      <button
                        onClick={() => handleOpenApproveModal(exchange)}
                        className="w-[98px] h-[38px] cursor-pointer rounded-md relative text-[#20523C] text-[16px] font-semibold leading-[22px]"
                        style={{
                          background:
                            "radial-gradient(65.83% 94.77% at 50.34% 116.3%, #C3F63C 0%, #54D10E 100%)",
                          border: "1px solid rgba(255, 255, 255, 0.4)",
                          boxShadow:
                            "0px 4px 4px 0px #FFFFFF52 inset, 0px -4px 4px 0px #FFFFFF52 inset",
                        }}
                      >
                        Approve
                      </button>
                      <div
                        className="flex justify-end relative"
                        ref={dropdownRef}
                      >
                        <button
                          onClick={() => toggleDropdown(exchange.id)}
                          className="text-[#585858] hover:text-[#111111] transition-colors cursor-pointer"
                        >
                          <ThreeDots />
                        </button>
                        {openDropdown === exchange.id && (
                          <div
                            className="absolute right-0 flex flex-col top-8 mt-1 h-fit w-48 bg-white border border-[#DEDEDE] rounded-md shadow-lg z-10"
                            role="menu"
                            aria-orientation="vertical"
                            aria-labelledby="options-menu"
                            ref={dropdownRef}
                            onClick={e => e.stopPropagation()}
                          >
                            <div className="py-1 flex-col flex">
                              <button
                                onClick={e => {
                                  e.stopPropagation();
                                  handleHold(exchange.id);
                                }}
                                className="w-full cursor-pointer text-left px-4 py-2.5 text-[14px] text-[#111111] hover:bg-[#F1F1F1] transition-colors"
                                role="menuitem"
                              >
                                Hold for a moment
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination Controls */}
              {sortedData.length > 0 && (
                <div className="flex justify-between items-center mt-4 px-4">
                  <div className="text-[14px] text-[#585858]">
                    Showing {(currentPage - 1) * rowsPerPage + 1} to{" "}
                    {Math.min(currentPage * rowsPerPage, sortedData.length)} of{" "}
                    {sortedData.length} entries
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() =>
                        setCurrentPage((prev) => Math.max(prev - 1, 1))
                      }
                      disabled={currentPage === 1}
                      className={`px-3 py-1 rounded cursor-pointer border ${
                        currentPage === 1
                          ? "border-[#DEDEDE] text-[#DEDEDE] cursor-not-allowed"
                          : "border-[#3BEE5C] text-[#3BEE5C] hover:bg-[#3BEE5C] hover:text-white"
                      }`}
                    >
                      Previous
                    </button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                      (page) => (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`w-8 h-8 rounded cursor-pointer ${
                            currentPage === page
                              ? "bg-[#3BEE5C] text-white"
                              : "border border-[#DEDEDE] text-[#585858] hover:border-[#3BEE5C]"
                          }`}
                        >
                          {page}
                        </button>
                      )
                    )}
                    <button
                      onClick={() =>
                        setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                      }
                      disabled={currentPage === totalPages}
                      className={`px-3 py-1 rounded border cursor-pointer ${
                        currentPage === totalPages
                          ? "border-[#DEDEDE] text-[#DEDEDE] cursor-not-allowed"
                          : "border-[#3BEE5C] text-[#3BEE5C] hover:bg-[#3BEE5C] hover:text-white"
                      }`}
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Mobile Card View */}
          <div className="lg:hidden space-y-4">
            {paginatedData.map((exchange) => (
              <div
                key={exchange.id}
                className="bg-white rounded-[10px] border border-[#DEDEDE] p-4"
              >
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="text-[14px] font-normal leading-[20px] text-[#111111] mb-2">
                      {exchange.name}
                    </h3>
                    <p className="text-[14px] font-normal leading-[20px] text-[#111111] mb-2">
                      {exchange.address}
                    </p>
                    <span className="text-[14px] font-medium leading-[20px] text-[#111111] mb-2">
                      {exchange.status}
                    </span>
                  </div>
                  <div className="relative ml-2">
                    <button
                      onClick={() => openMobileModal(exchange.id)}
                      className="transition-colors cursor-pointer"
                    >
                      <ThreeDots />
                    </button>
                  </div>
                </div>
                <div className="mt-4 flex items-end justify-end">
                  <button
                    onClick={() => handleOpenApproveModal(exchange)}
                    className="w-[98px] h-[38px] cursor-pointer rounded-md relative text-[#20523C] text-[16px] font-semibold leading-[22px]"
                    style={{
                      background:
                        "radial-gradient(65.83% 94.77% at 50.34% 116.3%, #C3F63C 0%, #54D10E 100%)",
                      border: "1px solid rgba(255, 255, 255, 0.4)",
                      boxShadow:
                        "0px 4px 4px 0px #FFFFFF52 inset, 0px -4px 4px 0px #FFFFFF52 inset",
                    }}
                  >
                    Approve
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Mobile Bottom Sheet Modal */}
        {showMobileModal !== null && successExchangeId === null && (
          <div
            className="lg:hidden fixed inset-0 z-50 modal-backdrop"
            style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
          >
            <div className="fixed bottom-0 left-0 right-0 bg-white rounded-t-[20px] shadow-2xl transform transition-transform duration-300 ease-out">
              <div className="flex justify-center pt-2 pb-[19px]">
                <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
              </div>

              <div className="space-y-1 mb-6">
                <button
                  onClick={() => showMobileModal && handleHold(showMobileModal)}
                  className="px-5 w-full cursor-pointer text-left py-3 text-[14px] font-normal leading-[20px] text-[#111111] border-b border-[#DEDEDE] transition-colors hover:bg-gray-50"
                >
                  Hold for a moment
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {rejectingExchangeId !== null && (
        <RejectThisRequest
          open={true}
          onClose={closeRejectModal}
          onSend={(reason, message) => {
            if (rejectingExchangeId !== null) {
              handleRejectSubmit(rejectingExchangeId, reason, message);
            }
          }}
          loading={rejectLoading}
        />
      )}

      {successExchangeId !== null && (
        <RejectSuccessModal open={true} onClose={handleCloseSuccessModal} />
      )}

      {approveModalOpen && selectedExchange && (
        <ApproveModal
          open={approveModalOpen}
          onClose={() => {
            setApproveModalOpen(false);
            setSelectedExchange(null);
          }}
          onApprove={handleApproveFromModal}
          onReject={() => {
            setApproveModalOpen(false);
            if (selectedExchange) {
              setShowRejectFromApprove(true);
            }
          }}
          data={{
            email: "<EMAIL>",
            officeName: selectedExchange.name,
            commercialRegNumber: "EOR/53C81839N2",
            currencyLicenseNumber: "1178300037475N2",
            address: selectedExchange.address,
            city: "Casablanca",
            province: "Casablanca Business Hub",
            primaryPhone: "+212 6 1234 5678",
            whatsapp: "+212 6 1234 5678",
            geolocation: "", 
          }}
        />
      )}

      {showRejectFromApprove && selectedExchange && (
        <RejectThisRequest
          open={showRejectFromApprove}
          onClose={() => setShowRejectFromApprove(false)}
          onSend={(reason, message) => {
            if (selectedExchange) {
              handleRejectSubmit(selectedExchange.id, reason, message);
            }
          }}
          loading={rejectLoading}
        />
      )}

      {approveSuccessModalOpen && (
        <ApproveSuccessModal
          open={true}
          onClose={handleCloseApproveSuccessModal}
        />
      )}
    </AdminLayout>
  );
};

export default RegisterRequest;
