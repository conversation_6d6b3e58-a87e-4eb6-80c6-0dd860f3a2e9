"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";

const BlogRedirect: React.FC = () => {
  const router = useRouter();

  useEffect(() => {
    // Redirect to root since blog is now the main page
    router.replace("/");
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to blog...</p>
      </div>
    </div>
  );
};

export default BlogRedirect;
