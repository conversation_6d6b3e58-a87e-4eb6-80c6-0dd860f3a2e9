"use client";
import React from "react";
import Link from "next/link";

const BlogPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">ExchanGo Blog</h1>
            </div>
            <nav className="flex space-x-8">
              <Link href="/exchange" className="text-gray-600 hover:text-gray-900">
                Exchange
              </Link>
              <Link href="/about-us" className="text-gray-600 hover:text-gray-900">
                About
              </Link>
              <Link href="/faq" className="text-gray-600 hover:text-gray-900">
                FAQ
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-600 to-green-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Welcome to ExchanGo Blog
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Stay updated with the latest currency exchange insights, market trends, and financial tips
          </p>
        </div>
      </section>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Sample Blog Posts */}
          <article className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="h-48 bg-gray-200"></div>
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-2">Understanding Currency Exchange Rates</h2>
              <p className="text-gray-600 mb-4">
                Learn the fundamentals of how exchange rates work and what factors influence them.
              </p>
              <Link href="#" className="text-green-600 hover:text-green-700 font-medium">
                Read More →
              </Link>
            </div>
          </article>

          <article className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="h-48 bg-gray-200"></div>
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-2">Best Times to Exchange Money</h2>
              <p className="text-gray-600 mb-4">
                Discover the optimal timing strategies for currency exchange to maximize your value.
              </p>
              <Link href="#" className="text-green-600 hover:text-green-700 font-medium">
                Read More →
              </Link>
            </div>
          </article>

          <article className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="h-48 bg-gray-200"></div>
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-2">Digital vs Physical Exchange</h2>
              <p className="text-gray-600 mb-4">
                Compare the pros and cons of digital currency exchange platforms versus traditional methods.
              </p>
              <Link href="#" className="text-green-600 hover:text-green-700 font-medium">
                Read More →
              </Link>
            </div>
          </article>
        </div>

        {/* Call to Action */}
        <section className="mt-16 text-center">
          <div className="bg-gray-50 rounded-lg p-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to Find the Best Exchange Rates?
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Use our platform to compare rates from multiple exchange offices
            </p>
            <Link
              href="/exchange"
              className="inline-block bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
            >
              Start Comparing Rates
            </Link>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">ExchanGo</h3>
              <p className="text-gray-400">
                Your trusted platform for finding the best currency exchange rates.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Company</h4>
              <ul className="space-y-2">
                <li><Link href="/about-us" className="text-gray-400 hover:text-white">About us</Link></li>
                <li><Link href="#" className="text-gray-400 hover:text-white">Career</Link></li>
                <li><Link href="#" className="text-gray-400 hover:text-white">Partnership</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Features</h4>
              <ul className="space-y-2">
                <li><Link href="/results" className="text-gray-400 hover:text-white">Exchange Rates</Link></li>
                <li><Link href="#" className="text-gray-400 hover:text-white">Alerts</Link></li>
                <li><Link href="#" className="text-gray-400 hover:text-white">Mobile App</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Support</h4>
              <ul className="space-y-2">
                <li><Link href="/faq" className="text-gray-400 hover:text-white">FAQ</Link></li>
                <li><Link href="#" className="text-gray-400 hover:text-white">Help Center</Link></li>
                <li><Link href="#" className="text-gray-400 hover:text-white">Contact</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center">
            <p className="text-gray-400">© 2024 ExchanGo. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default BlogPage;
