{"name": "exchango-24", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3003", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.0", "@types/mapbox-gl": "^3.4.1", "axios": "^1.6.0", "chart.js": "^4.5.0", "date-fns": "^4.1.0", "framer-motion": "^12.16.0", "lucide-react": "^0.503.0", "mapbox-gl": "^3.12.0", "moment": "^2.30.1", "next": "15.2.4", "rc-time-picker": "^3.7.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-ga4": "^2.1.0", "react-loading-skeleton": "^3.5.0", "react-phone-number-input": "^3.4.12", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "swiper": "^11.2.8", "yet-another-react-lightbox": "^3.23.4"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/jquery": "^3.5.32", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "typescript": "^5"}}