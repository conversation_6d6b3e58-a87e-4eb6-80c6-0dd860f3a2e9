import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Detail Article Title Truncation
export const truncateTitleSmart = (
  title: string,
  maxChars: number = 80
): string => {
  if (title.length <= maxChars) return title;
  const truncated = title.slice(0, maxChars);
  return truncated.slice(0, truncated.lastIndexOf(" ")) + "...";
};

// Split Title into Two Lines
export type SplitTitle = {
  firstLine: string;
  secondLine: string;
};

// Smart Title Split
export const splitTitleSmart = (title: string): SplitTitle => {
  const words = title.trim().split(" ");
  if (words.length <= 3) {
    return { firstLine: title, secondLine: "" };
  }
  const mid = Math.ceil(words.length / 2);
  const firstLine = words.slice(0, mid).join(" ");
  const secondLine = words.slice(mid).join(" ");
  return { firstLine, secondLine };
};

// Format Date
export function formatDate(date?: string) {
  if (!date) return "Unknown date";
  const d = new Date(date);
  return isNaN(d.getTime())
    ? "Invalid date"
    : d.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
}

// BlogListing page Title Truncation - Updated for better display
export const truncateTitle = (title: string, maxLength = 60) => {
  if (title.length <= maxLength) return title;
  const truncated = title.slice(0, maxLength);
  // Find the last space to avoid cutting words in half
  const lastSpace = truncated.lastIndexOf(" ");
  return lastSpace > 0 
    ? truncated.slice(0, lastSpace) + "..."
    : truncated + "...";
};

// BlogListing page Description Truncation - Updated for better display
// export const truncateDescription = (desc: string, maxLength = 120) => {
//   if (desc.length <= maxLength) return desc;
//   const truncated = desc.slice(0, maxLength);
//   // Find the last space to avoid cutting words in half
//   const lastSpace = truncated.lastIndexOf(" ");
//   return lastSpace > 0 
//     ? truncated.slice(0, lastSpace) + "..."
//     : truncated + "...";
// };