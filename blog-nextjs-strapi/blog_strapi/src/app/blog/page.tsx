import Hero from "@/components/BlogHero";
import CTA from "@/components/CTA";
import ClientBlogPage from "@/components/BlogListing";

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_URL;

const fetchArticles = async () => {
  const res = await fetch(`${STRAPI_URL}/api/articles?populate=*`, {
    cache: "no-store",
  });
  const json = await res.json();
  return json?.data || [];
};

export default async function BlogPage() {
  const articles = await fetchArticles();

  return (
    <main className="min-h-screen bg-white">
      <Hero />
      <ClientBlogPage articles={articles} />
      <CTA />
    </main>
  );
}
