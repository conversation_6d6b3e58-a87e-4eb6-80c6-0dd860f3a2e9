"use client";

import axios from "axios";
import Image from "next/image";
import Link from "next/link";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import rehypeSanitize from "rehype-sanitize";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import CTA from "@/components/CTA";
import Navigation from "@/components/Navigation";
import { FaFacebookF, FaTwitter, FaInstagram } from "react-icons/fa";
import OtherArticles from "@/components/OtherArticles";
import WhatWeStandFor from "@/components/WhatWeStandFor";

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_URL;

interface ImageFormat {
  url: string;
}

interface Avatar {
  url: string;
  formats?: {
    thumbnail?: ImageFormat;
    small?: ImageFormat;
    medium?: ImageFormat;
    large?: ImageFormat;
    [key: string]: ImageFormat | undefined;
  };
}

interface Author {
  id: string;
  name: string;
  Headline: string;
  avatar: Avatar;
}

interface Category {
  id: string;
  name: string;
}

interface RichTextBlock {
  id: string;
  __component: "shared.rich-text";
  body?: string;
  content?: string;
}

interface MediaBlock {
  id: string;
  __component: "shared.media";
  file: MediaFile;
  caption?: string;
}

interface QuoteBlock {
  id: string;
  __component: "shared.quote";
  text: string;
  author?: string;
}

interface MediaFile {
  data?: {
    attributes?: {
      url: string;
      alternativeText?: string;
    };
  };
  url?: string;
  alternativeText?: string;
}

interface SliderBlock {
  id: string;
  __component: "shared.slider";
  files: MediaFile[];
  caption?: string;
}

type Block = RichTextBlock | MediaBlock | QuoteBlock | SliderBlock;

interface Article {
  id: string;
  title: string;
  description: string;
  slug: string;
  cover: { url: string; alternativeText?: string };
  author: Author;
  category: Category;
  blocks: Block[];
  publishedAt: string;
}

interface AuthorArticle {
  id: string;
  title: string;
  slug: string;
  description: string;
  publishedAt: string;
  author: {
    id: string;
    name: string;
  };
  cover: {
    url: string;
    alternativeText: string;
  };
}

interface RawArticleData {
  id: string;
  slug: string;
  title: string;
  description: string;
  publishedAt: string;
  cover?: {
    url: string;
    alternativeText?: string;
  };
  author?: {
    id: string;
    name: string;
    Headline: string;
    avatar?: {
      url: string;
    };
  };
  category?: {
    id: string;
    name: string;
  };
  blocks?: Block[];
}

const socialLinks = [
  {
    name: "facebook",
    icon: <FaFacebookF />,
    href: "https://facebook.com",
  },
  {
    name: "twitter",
    icon: <FaTwitter />,
    href: "https://twitter.com",
  },
  {
    name: "instagram",
    icon: <FaInstagram />,
    href: "https://instagram.com",
  },
];

// Helper function to truncate title for breadcrumb
const truncateBreadcrumbTitle = (title: string) => {
  return title.length > 25 ? title.substring(0, 22) + "..." : title;
};

export default function BlogDetailPage() {
  const { slug } = useParams();
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);
  const [authorArticles, setAuthorArticles] = useState<AuthorArticle[]>([]);

  const fetchArticle = async (slug: string) => {
    try {
      const res = await axios.get(
        `${STRAPI_URL}/api/articles?filters[slug][$eq]=${slug}&populate=*`
      );

      const raw: RawArticleData = res.data.data[0];

      if (raw) {
        const normalized: Article = {
          id: raw.id,
          slug: raw.slug,
          title: raw.title,
          description: raw.description,
          publishedAt: raw.publishedAt,
          cover: {
            url: raw.cover?.url || "",
            alternativeText: raw.cover?.alternativeText || "",
          },
          author: {
            id: raw.author?.id || "",
            name: raw.author?.name || "",
            Headline: raw.author?.Headline || "",
            avatar: {
              url: raw.author?.avatar?.url || "",
            },
          },
          category: {
            id: raw.category?.id || "",
            name: raw.category?.name || "",
          },
          blocks: raw.blocks || [],
        };

        setArticle(normalized);
      } else {
        console.warn("No article found for slug:", slug);
      }
    } catch (error) {
      console.error("Failed to fetch article:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAuthorArticles = async (
    authorId: string,
    currentArticleId: string
  ) => {
    try {
      const res = await axios.get(
        `${STRAPI_URL}/api/articles?filters[author][id][$eq]=${authorId}&populate=*`
      );

      const authorArticles: AuthorArticle[] = res.data.data
        .filter((a: RawArticleData) => a.id.toString() !== currentArticleId)
        .map(
          (raw: RawArticleData): AuthorArticle => ({
            id: raw.id,
            title: raw.title,
            slug: raw.slug,
            description: raw.description,
            publishedAt: raw.publishedAt,
            author: {
              id: raw.author?.id || "",
              name: raw.author?.name || "",
            },
            cover: {
              url: raw.cover?.url || "",
              alternativeText: raw.cover?.alternativeText || "",
            },
          })
        );

      setAuthorArticles(authorArticles);
    } catch (error) {
      console.error("Failed to fetch author articles:", error);
    }
  };

  useEffect(() => {
    if (article?.author?.id && article.id) {
      fetchAuthorArticles(article.author.id, article.id);
    }
  }, [article]);

  useEffect(() => {
    if (slug) fetchArticle(slug as string);
  }, [slug]);

  const formatDate = (date: string) =>
    new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });

  if (loading || !article) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="flex flex-col items-center gap-4">
          <div className="w-12 h-12 border-4 border-green-500 border-dashed rounded-full animate-spin"></div>
          <p className="text-sm text-gray-500 tracking-wide">
            Fetching the article...
          </p>
        </div>
      </div>
    );
  }

  if (!article) return <div>Not found</div>;

  return (
    <div className="bg-white">
      <Navigation />

      <div className="absolute top-[88px] left-0 w-full z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="text-xs sm:text-sm text-white/90 pt-4 sm:pt-6 md:pt-8 text-left">
            <Link
              href="/blog"
              className="hover:text-white transition-colors duration-200 underline decoration-transparent hover:decoration-white"
            >
              Blog and article
            </Link>
            <span className="mx-1.5 sm:mx-2 text-green-400">/</span>
            <span className="text-green-400 font-medium">
              {truncateBreadcrumbTitle(article.title)}
            </span>
          </nav>
        </div>
      </div>

      <section className="relative">
        {/* Extended Background Image - Responsive heights */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat overflow-hidden"
          style={{
            backgroundImage: `url('/images/blog_detail_page_bg.png')`,
            // Mobile: extension to middle of cover image (rectangle)
            height: "calc(50vh + 60px)", // Mobile: reduced to match rectangle cover image
            zIndex: 0,
          }}
        />

        {/* Desktop-specific background extension */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat overflow-hidden hidden md:block"
          style={{
            backgroundImage: `url('/images/blog_detail_page_bg.png')`,
            height: "calc(100vh + 140px)", // Desktop: full extension
            zIndex: 0,
          }}
        />

        {/* Overlay for extended background */}
        <div
          className="absolute inset-0"
          style={{
            height: "calc(70vh + 70px)", // Mobile overlay matches rectangle
            zIndex: 1,
          }}
        />

        {/* Desktop overlay */}
        <div
          className="absolute inset-0 hidden md:block"
          style={{
            height: "calc(100vh + 140px)", // Desktop overlay
            zIndex: 1,
          }}
        />

        {/* HERO SECTION */}
        <div className="relative z-10 min-h-[40vh] md:min-h-[70vh] flex flex-col justify-center px-4 sm:px-6 lg:px-8 pt-32 sm:pt-40 md:pt-32 pb-2 md:pb-8">
          <div className="max-w-7xl mx-auto w-full text-center">
            <div className="max-w-5xl mx-auto pt-0 md:pt-6">
              {/* Title */}
              <h1
                className="text-xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-2 md:mb-8 leading-tight tracking-tight break-words px-2 sm:px-0"
                style={{
                  wordBreak: "break-word",
                  overflowWrap: "break-word",
                  textShadow: "0 2px 8px rgba(0, 0, 0, 0.4)",
                  lineHeight: "1.2",
                }}
              >
                {article.title}
              </h1>

              {/* Description */}
              {article.description && (
                <p className="text-sm sm:text-base md:text-lg text-white/70 mb-2 md:mb-6 leading-relaxed font-light max-w-4xl mx-auto px-2 sm:px-0">
                  {article.description}
                </p>
              )}

              {/* Published Date */}
              <p className="text-sm sm:text-base md:text-lg lg:text-xl text-green-400 font-medium px-2 mb-1">
                Published {formatDate(article.publishedAt)}
              </p>
            </div>
          </div>
        </div>

        {/* COVER IMAGE - Mobile fit + Desktop rectangle */}
        {article.cover && article.cover.url && (
          <div className="relative z-20 px-4 sm:px-6 lg:px-8 mt-2 md:mt-4 mb-12 md:mb-16">
            <div className="max-w-7xl mx-auto">
              <div className="relative overflow-hidden rounded-lg sm:rounded-xl lg:rounded-2xl shadow-2xl">
                {/* Mobile: aspect-[4/3] for better fit, Desktop: ultra-wide rectangle */}
                <div className="aspect-[4/3] sm:aspect-[16/10] lg:aspect-[21/9]">
                  <Image
                    src={`${STRAPI_URL}${article.cover.url}`}
                    alt={article.cover.alternativeText ?? article.title}
                    fill
                    className="object-cover"
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 1200px"
                    priority
                    onError={() => {
                      console.error(
                        "Image failed to load:",
                        `${STRAPI_URL}${article.cover.url}`
                      );
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </section>

      {/* ARTICLE DETAIL CONTENT SECTION */}
      <main className="relative z-10 bg-white -mt-8 md:-mt-32">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-0 pb-12 sm:pb-16">
          <div className="space-y-6 md:space-y-12 -mt-4 md:-mt-16 relative z-30">
            {/* Responsive separator */}
            <div className="h-4 md:h-16"></div>

            {article.blocks?.map((block: Block, idx: number) => {
              switch (block.__component) {
                case "shared.rich-text": {
                  const richTextBlock = block as RichTextBlock;
                  const markdownContent =
                    richTextBlock.body || richTextBlock.content || "";

                  return (
                    <div key={idx} className="prose prose-lg max-w-none">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        rehypePlugins={[rehypeRaw, rehypeSanitize]}
                        components={{
                          h1: ({ children }) => (
                            <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight mt-6 md:mt-12 first:mt-0">
                              {children}
                            </h1>
                          ),
                          h2: ({ children }) => (
                            <h2 className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-gray-900 mb-3 sm:mb-4 lg:mb-6 leading-tight mt-5 md:mt-10">
                              {children}
                            </h2>
                          ),
                          h3: ({ children }) => (
                            <h3 className="text-base sm:text-lg lg:text-xl xl:text-2xl font-bold text-gray-900 mb-3 sm:mb-4 leading-tight mt-4 md:mt-8">
                              {children}
                            </h3>
                          ),
                          h4: ({ children }) => (
                            <h4 className="text-base sm:text-lg lg:text-xl font-semibold text-gray-900 mb-2 sm:mb-3 lg:mb-4 leading-tight mt-3 md:mt-6">
                              {children}
                            </h4>
                          ),
                          p: ({ children }) => (
                            <p className="text-sm sm:text-base lg:text-lg leading-relaxed text-gray-700 mb-4 sm:mb-5 lg:mb-6">
                              {children}
                            </p>
                          ),
                          ul: ({ children }) => (
                            <ul className="text-sm sm:text-base lg:text-lg text-gray-700 mb-4 sm:mb-5 lg:mb-6 pl-4 sm:pl-6 lg:pl-8 space-y-1 sm:space-y-2 lg:space-y-3 list-disc">
                              {children}
                            </ul>
                          ),
                          ol: ({ children }) => (
                            <ol className="text-sm sm:text-base lg:text-lg text-gray-700 mb-4 sm:mb-5 lg:mb-6 pl-4 sm:pl-6 lg:pl-8 space-y-1 sm:space-y-2 lg:space-y-3 list-decimal">
                              {children}
                            </ol>
                          ),
                          li: ({ children }) => (
                            <li className="leading-relaxed mb-1 sm:mb-2">
                              {children}
                            </li>
                          ),
                          strong: ({ children }) => (
                            <strong className="font-semibold text-gray-900">
                              {children}
                            </strong>
                          ),
                          em: ({ children }) => (
                            <em className="italic text-gray-700">{children}</em>
                          ),
                          blockquote: ({ children }) => (
                            <blockquote className="border-l-4 border-green-500 pl-4 sm:pl-6 lg:pl-8 italic text-gray-600 my-6 sm:my-8 text-sm sm:text-base lg:text-lg xl:text-xl bg-gray-50 py-4 rounded-r-lg">
                              {children}
                            </blockquote>
                          ),
                          code: ({ children }) => (
                            <code className="bg-gray-100 px-1.5 sm:px-2 py-1 rounded text-xs sm:text-sm font-mono text-gray-800">
                              {children}
                            </code>
                          ),
                          pre: ({ children }) => (
                            <pre className="bg-gray-100 p-3 sm:p-4 lg:p-6 rounded-lg overflow-x-auto mb-4 sm:mb-6 text-xs sm:text-sm">
                              {children}
                            </pre>
                          ),
                          table: ({ children }) => (
                            <div className="overflow-x-auto mb-4 sm:mb-6">
                              <table className="min-w-full border-collapse border border-gray-300 text-sm sm:text-base">
                                {children}
                              </table>
                            </div>
                          ),
                          th: ({ children }) => (
                            <th className="border border-gray-300 px-2 sm:px-3 lg:px-4 py-2 bg-gray-50 font-semibold text-left text-xs sm:text-sm lg:text-base">
                              {children}
                            </th>
                          ),
                          td: ({ children }) => (
                            <td className="border border-gray-300 px-2 sm:px-3 lg:px-4 py-2 text-xs sm:text-sm lg:text-base">
                              {children}
                            </td>
                          ),
                          a: ({ children, href }) => (
                            <a
                              href={href}
                              className="text-green-600 hover:text-green-700 underline transition-colors duration-200"
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {children}
                            </a>
                          ),
                        }}
                      >
                        {markdownContent}
                      </ReactMarkdown>
                    </div>
                  );
                }
                case "shared.media": {
                  const mediaBlock = block as MediaBlock;
                  const imageUrl =
                    mediaBlock.file?.data?.attributes?.url ||
                    mediaBlock.file?.url;
                  const altText =
                    mediaBlock.file?.data?.attributes?.alternativeText ||
                    mediaBlock.file?.alternativeText ||
                    "";

                  if (!imageUrl) {
                    console.warn("Media block missing image URL:", mediaBlock);
                    return null;
                  }

                  return (
                    <div key={idx} className="my-6 sm:my-8 lg:my-12">
                      <div className="relative overflow-hidden rounded-lg sm:rounded-xl lg:rounded-2xl shadow-lg">
                        <Image
                          src={STRAPI_URL + imageUrl}
                          alt={altText}
                          width={1000}
                          height={600}
                          className="object-cover w-full h-auto"
                        />
                      </div>
                      {mediaBlock.caption && (
                        <p className="text-xs sm:text-sm lg:text-base text-center mt-3 sm:mt-4 lg:mt-6 italic text-gray-500">
                          {mediaBlock.caption}
                        </p>
                      )}
                    </div>
                  );
                }
                case "shared.quote": {
                  const quoteBlock = block as QuoteBlock;
                  return (
                    <blockquote
                      key={idx}
                      className="border-l-4 border-green-500 pl-4 sm:pl-6 lg:pl-8 italic text-gray-600 my-6 sm:my-8 lg:my-12 text-sm sm:text-base lg:text-lg xl:text-xl bg-gray-50 py-4 rounded-r-lg"
                    >
                      &quot;{quoteBlock.text}&quot;
                      {quoteBlock.author && (
                        <span className="block mt-2 sm:mt-3 lg:mt-4 not-italic font-medium text-gray-800">
                          — {quoteBlock.author}
                        </span>
                      )}
                    </blockquote>
                  );
                }
                case "shared.slider": {
                  const sliderBlock = block as SliderBlock;
                  if (!sliderBlock.files || sliderBlock.files.length === 0) {
                    return null;
                  }

                  return (
                    <div key={idx} className="my-6 sm:my-8 lg:my-12">
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
                        {sliderBlock.files.map((file, fileIdx) => {
                          const imageUrl =
                            file?.data?.attributes?.url || file?.url;
                          const altText =
                            file?.data?.attributes?.alternativeText ||
                            file?.alternativeText ||
                            "";

                          if (!imageUrl) return null;

                          return (
                            <div
                              key={fileIdx}
                              className="relative overflow-hidden rounded-lg sm:rounded-xl lg:rounded-2xl shadow-lg"
                            >
                              <Image
                                src={STRAPI_URL + imageUrl}
                                alt={altText}
                                width={400}
                                height={300}
                                className="object-cover w-full h-auto"
                              />
                            </div>
                          );
                        })}
                      </div>
                      {sliderBlock.caption && (
                        <p className="text-xs sm:text-sm lg:text-base text-center mt-3 sm:mt-4 lg:mt-6 italic text-gray-500">
                          {sliderBlock.caption}
                        </p>
                      )}
                    </div>
                  );
                }
                default:
                  return null;
              }
            })}
          </div>
        </div>
      </main>

      {/* AUTHOR SECTION */}
      {article.author && (
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 border-t border-gray-200 pt-5 sm:pt-8 sm:mt-4 pb-12 sm:pb-20">
          <div className="flex flex-row justify-between items-center">
            {/* Author Info */}
            <div className="flex items-center space-x-3 sm:space-x-4 lg:space-x-6">
              <div className="relative w-10 h-10 sm:w-16 sm:h-16 lg:w-20 lg:h-20 rounded-full overflow-hidden flex-shrink-0">
                <Image
                  src={"/images/samanta.png"}
                  alt={article.author.name || "Author"}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="text-left">
                <p className="text-sm sm:text-lg lg:text-xl font-semibold text-gray-900 mb-0.5 sm:mb-1">
                  {article.author.name}
                </p>
                <p className="text-xs sm:text-base lg:text-lg text-gray-600">
                  {article.author.Headline}
                </p>
              </div>
            </div>

            {/* Social Icons */}
            <div className="flex space-x-1.5 sm:space-x-4 lg:space-x-6 ml-auto sm:ml-0">
              {socialLinks.map(({ name, icon, href }) => (
                <Link
                  key={name}
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={name}
                  className="w-6 h-6 sm:w-10 sm:h-10 lg:w-12 lg:h-12 rounded-full border border-gray-200 flex items-center justify-center transition-all duration-300 hover:bg-green-500 text-gray-500 hover:text-black hover:border-green-500"
                >
                  <span className="text-xs sm:text-base lg:text-lg">
                    {icon}
                  </span>
                </Link>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* OTHER ARTICLES */}
      <OtherArticles articles={authorArticles} currentAuthor={article.author} />

      {/* CTA SECTION */}
      {/* Desktop & Tablet CTA */}
      <div className="hidden sm:block px-4 sm:px-6 lg:px-8 pb-12">
        <CTA />
      </div>
      {/* Mobile CTA */}
      <div className="block sm:hidden px-4 sm:px-6 lg:px-8 pb-12">
        <WhatWeStandFor />
      </div>
    </div>
  );
}
