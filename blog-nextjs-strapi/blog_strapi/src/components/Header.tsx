"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";
import { useState, useCallback } from "react";
import { usePathname } from "next/navigation";

// Navigation configuration - easily maintainable
const NAV_LINKS = [
  { label: "About", href: "/about" },
  { label: "Register my office", href: "/register" },
  { label: "FAQ", href: "/faq" },
  { label: "Blog", href: "/blog" },
] as const;

// Hamburger menu icon component - Wider lines for better visibility
const HamburgerIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    viewBox="0 0 28 24"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M2 6h24M2 12h24M2 18h24"
    />
  </svg>
);

export default function Header() {
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const pathname = usePathname();

  // Close mobile menu when navigation occurs
  const handleNavigation = useCallback(() => {
    setIsSheetOpen(false);
  }, []);

  // Check if current path matches link
  const isActiveLink = useCallback(
    (href: string) => {
      return pathname === href;
    },
    [pathname]
  );

  // Handle CTA button click
  const handleCTAClick = useCallback(() => {
    // Add your CTA logic here (e.g., open modal, navigate to exchange page)
    console.log("Exchange office space clicked");
    // Example: router.push('/exchange') or open modal
  }, []);

  return (
    <header className="bg-white w-full">
      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 h-[88px] flex items-center justify-between">
        <div className="flex items-center space-x-4 sm:space-x-8 md:space-x-10">
          {/* Logo */}
          <Link
            href="/"
            className="flex items-center hover:opacity-80 transition-opacity duration-200"
            prefetch={false}
            aria-label="ExchanGo 24 Home"
          >
            <Image
              src="/images/Logo.png"
              alt="ExchanGo 24"
              width={160}
              height={40}
              className="h-10 w-auto"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav
            className="hidden lg:flex items-center space-x-6 xl:space-x-8"
            role="navigation"
          >
            {NAV_LINKS.map((link) => (
              <Link
                key={link.label}
                href={link.href}
                className={`text-sm lg:text-base cursor-pointer transition-colors duration-200 font-medium tracking-wide ${
                  isActiveLink(link.href)
                    ? "text-green-800 font-semibold"
                    : "text-gray-600 hover:text-gray-900"
                }`}
                prefetch={false}
              >
                {link.label}
              </Link>
            ))}
          </nav>
        </div>

        {/* Desktop CTA Button */}
        <div className="hidden lg:block">
          <Button
            variant="outline"
            onClick={handleCTAClick}
            className="text-green-800 border-green-800 bg-transparent hover:bg-green-800 hover:text-white hover:border-green-800 px-3 py-2 text-sm lg:text-base rounded-sm transition-all duration-200  tracking-wide"
          >
            Exchange office Space
          </Button>
        </div>

        {/* Mobile Menu */}
        <div className="lg:hidden">
          <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="text-gray-800 h-12 w-12 p-2 sm:h-14 sm:w-14 sm:p-3"
                aria-label="Open navigation menu"
              >
                <HamburgerIcon className="w-32 h-32 sm:w-40 sm:h-36" />
              </Button>
            </SheetTrigger>

            <SheetContent
              side="right"
              className="bg-white text-gray-800 w-80 sm:w-96 border-l border-gray-200 px-6 py-8"
            >
              <SheetHeader className="mb-8">
                <SheetTitle className="text-lg sm:text-xl text-gray-800 text-left font-semibold tracking-wide">
                  Menu
                </SheetTitle>
              </SheetHeader>

              <nav className="space-y-4" role="navigation">
                {NAV_LINKS.map((link) => (
                  <Link
                    key={link.label}
                    href={link.href}
                    onClick={handleNavigation}
                    className={`block text-base sm:text-lg py-3 px-4 rounded-lg transition-colors duration-200 font-medium tracking-wide ${
                      isActiveLink(link.href)
                        ? "text-green-800 font-semibold bg-green-50"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                    }`}
                    prefetch={false}
                  >
                    {link.label}
                  </Link>
                ))}

                {/* Mobile CTA Button */}
                <div className="pt-6 mt-8 border-t border-gray-200">
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleCTAClick();
                      handleNavigation();
                    }}
                    className="text-green-800 border-green-800 bg-transparent hover:bg-green-800 hover:text-white hover:border-green-800 w-full px-6 py-4 text-base sm:text-lg rounded-md transition-all duration-200 tracking-wide"
                  >
                    Exchange office Space
                  </Button>
                </div>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}