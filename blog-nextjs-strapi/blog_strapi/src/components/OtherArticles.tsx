import Image from "next/image";
import Link from "next/link";
import { formatDate } from "@/lib/utils";

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_URL;

interface Article {
  id: string;
  slug: string;
  title: string;
  description: string;
  publishedAt: string;
  author: {
    id: string;
    name: string;
  };
  cover: {
    url: string;
    alternativeText?: string;
  };
}

interface OtherArticlesProps {
  articles: Article[];
  currentAuthor?: {
    id: string;
    name: string;
  };
}

const OtherArticles = ({ articles, currentAuthor }: OtherArticlesProps) => {
  if (!articles || articles.length === 0 || !currentAuthor) return null;

  // Filter articles by the same author
  const authorArticles = articles
    .filter((article) => article.author?.id === currentAuthor.id)
    .sort(() => 0.5 - Math.random())
    .slice(0, 4);

  // If no articles from same author, return null
  if (authorArticles.length === 0) return null;

  return (
    <section className="py-4 md:py-20">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-12">
        <h2 className="text-lg md:text-3xl font-semibold text-slate-900 mb-2 md:mb-4">
          Other Articles
        </h2>
        
        {/* Mobile: 2-column grid, Desktop: 4-column grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-8 pt-1 md:pt-4">
          {authorArticles.map((article) => (
            <Link
              key={article.id}
              href={`/blog/${article.slug}`}
              className="group"
            >
              <div className="bg-white overflow-hidden">
                {/* Mobile: Smaller image, Desktop: Original size */}
                <div className="relative w-full h-32 md:h-48 overflow-hidden">
                  <Image
                    src={
                      article.cover?.url.startsWith("http")
                        ? article.cover.url
                        : `${STRAPI_URL}${article.cover.url}`
                    }
                    alt={article.cover?.alternativeText || article.title}
                    fill
                    className="object-cover rounded-md"
                    sizes="(max-width: 768px) 50vw, 400px"
                  />
                </div>
                
                {/* Mobile: Reduced padding and text sizes */}
                <div className="p-2 md:p-4">
                  <p className="text-xs text-gray-400 mb-1 md:mb-2">
                    {formatDate(article.publishedAt)} •{" "}
                    {article.author?.name || "Unknown"}
                  </p>
                  <h3 className="text-sm md:text-base lg:text-lg xl:text-xl font-bold text-black hover:text-green-600 mb-1 md:mb-2">
                    {article.title}
                  </h3>
                  <p className="text-xs md:text-sm lg:text-base xl:text-lg text-gray-700 mb-1 md:mb-2">
                    {article.description}
                  </p>
                  <span className="text-green-600 text-xs md:text-sm font-medium group-hover:underline">
                    Read more
                  </span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default OtherArticles;