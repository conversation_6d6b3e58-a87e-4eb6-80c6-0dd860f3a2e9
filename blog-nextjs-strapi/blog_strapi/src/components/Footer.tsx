import Link from "next/link";
import Image from "next/image";
import { FaFacebookF, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaInstagram, FaYout<PERSON> } from "react-icons/fa";

function Footer() {
  return (
    <footer className="relative bg-white border-t text-sm text-gray-600">
      {/* Background Vector Image */}
      <div className="absolute bottom-0 left-0 w-full h-full z-0 overflow-hidden">
        <Image
          src="/images/vector.png"
          alt="Vector Background"
          fill
          className="object-contain object-left-bottom pointer-events-none select-none"
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Mobile Layout */}
        <div className="block md:hidden">
          {/* Navigation Sections */}
          <div className="space-y-6 mb-8">
            {/* Company Links */}
            <div className="text-center">
              <h3 className="text-gray-900 font-semibold mb-3">Company</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/about" className="text-gray-600 hover:text-black">About us</Link>
                </li>
                <li>
                  <Link href="/career" className="text-gray-600 hover:text-black">Career</Link>
                </li>
                <li>
                  <Link href="/partnership" className="text-gray-600 hover:text-black">Partnership</Link>
                </li>
                <li>
                  <Link href="/help" className="text-gray-600 hover:text-black">Help center</Link>
                </li>
                <li>
                  <Link href="/blog" className="text-gray-600 hover:text-black">Blog & news</Link>
                </li>
              </ul>
            </div>

            {/* Feature Links */}
            <div className="text-center">
              <h3 className="text-gray-900 font-semibold mb-3">Feature</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/location" className="text-gray-600 hover:text-black">Location</Link>
                </li>
                <li>
                  <Link href="/alerts" className="text-gray-600 hover:text-black">Alert reminder</Link>
                </li>
                <li>
                  <Link href="/exchange" className="text-gray-600 hover:text-black">Global exchange</Link>
                </li>
                <li>
                  <Link href="/how-it-works" className="text-gray-600 hover:text-black">How it works</Link>
                </li>
                <li>
                  <Link href="/testimony" className="text-gray-600 hover:text-black">Testimony</Link>
                </li>
              </ul>
            </div>

            {/* Other Links */}
            <div className="text-center">
              <h3 className="text-gray-900 font-semibold mb-3">Other</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/privacy" className="text-gray-600 hover:text-black">Privacy policy</Link>
                </li>
                <li>
                  <Link href="/cookies" className="text-gray-600 hover:text-black">Cookie Policy</Link>
                </li>
                <li>
                  <Link href="/legal" className="text-gray-600 hover:text-black">legal</Link>
                </li>
                <li>
                  <Link href="/complain" className="text-gray-600 hover:text-black">Complain</Link>
                </li>
                <li>
                  <Link href="/about" className="text-gray-600 hover:text-black">About</Link>
                </li>
                <li>
                  <Link href="/register" className="text-gray-600 hover:text-black">Register my office</Link>
                </li>
                <li>
                  <Link href="/faq" className="text-gray-600 hover:text-black">FAQ</Link>
                </li>
              </ul>
            </div>
          </div>

          {/* Logo and Contact Info */}
          <div className="text-center mb-6">
            <div className="mb-4">
              <Image
                src="/images/Logo.png"
                alt="ExchanGo24"
                width={160}
                height={40}
                className="h-8 w-auto mx-auto"
              />
            </div>
            <div className="space-y-1 text-gray-600">
              <p><EMAIL></p>
              <p>(626) 555-0129</p>
              <p>2972 Westheimer Rd. Santa Ana, Illinois 85486</p>
            </div>
          </div>

          {/* Social Icons */}
          <div className="flex justify-center gap-4 mb-0">
            <div className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-200 transition-colors duration-300 hover:bg-green-500 hover:text-black cursor-pointer">
              <FaFacebookF className="h-4 w-4 text-gray-500 " />
            </div>
            <div className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-200 transition-colors duration-300 hover:bg-green-500 hover:text-black cursor-pointer">
              <FaTwitter className="h-4 w-4 text-gray-500" />
            </div>
            <div className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-200 transition-colors duration-300 hover:bg-green-500 hover:text-black cursor-pointer">
              <FaInstagram  className="h-4 w-4 text-gray-500" />
            </div>
            <div className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-200 transition-colors duration-300 hover:bg-green-500 hover:text-black cursor-pointer">
              <FaYoutube className="h-4 w-4 text-gray-500" />
            </div>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden md:block">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-10">
            {/* Logo */}
            <div>
              <div className="mb-4">
                <Image
                  src="/images/Logo.png"
                  alt="ExchanGo24"
                  width={160}
                  height={40}
                  className="h-8 w-auto"
                />
              </div>
              <p className="mb-1"><EMAIL></p>
              <p className="mb-1">(626) 555-0129</p>
              <p className="text-sm">
                2972 Westheimer Rd. Santa Ana, Illinois 85486
              </p>
              <div className="flex gap-4 mt-4">
                <div className="w-8 h-8 flex items-center justify-center rounded-full transition-colors duration-300 hover:bg-green-500 hover:text-black cursor-pointer">
                  <FaFacebookF className="h-4 w-4 text-gray-500 hover:text-black" />
                </div>
                <div className="w-8 h-8 flex items-center justify-center rounded-full transition-colors duration-300 hover:bg-green-500 hover:text-black cursor-pointer">
                  <FaTwitter className="h-4 w-4 text-gray-500 hover:text-black" />
                </div>
                <div className="w-8 h-8 flex items-center justify-center rounded-full transition-colors duration-300 hover:bg-green-500 hover:text-black cursor-pointer">
                  <FaInstagram className="h-4 w-4 text-gray-500 hover:text-black" />
                </div>
                <div className="w-8 h-8 flex items-center justify-center rounded-full transition-colors duration-300 hover:bg-green-500 hover:text-black cursor-pointer">
                  <FaYoutube className="h-4 w-4 text-gray-500 hover:text-black" />
                </div>
              </div>
            </div>

            {/* Company Links */}
            <div>
              <h3 className="text-gray-900 font-semibold mb-4">Company</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/about" className="text-gray-600 hover:text-black">About us</Link>
                </li>
                <li>
                  <Link href="/career" className="text-gray-600 hover:text-black">Career</Link>
                </li>
                <li>
                  <Link href="/partnership" className="text-gray-600 hover:text-black">Partnership</Link>
                </li>
                <li>
                  <Link href="/help" className="text-gray-600 hover:text-black">Help center</Link>
                </li>
                <li>
                  <Link href="/blog" className="text-gray-600 hover:text-black">Blog & news</Link>
                </li>
              </ul>
            </div>

            {/* Feature Links */}
            <div>
              <h3 className="text-gray-900 font-semibold mb-4">Feature</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/location" className="text-gray-600 hover:text-black">Location</Link>
                </li>
                <li>
                  <Link href="/alerts" className="text-gray-600 hover:text-black">Alert reminder</Link>
                </li>
                <li>
                  <Link href="/exchange" className="text-gray-600 hover:text-black">Global exchange</Link>
                </li>
                <li>
                  <Link href="/how-it-works" className="text-gray-600 hover:text-black">How it works</Link>
                </li>
                <li>
                  <Link href="/testimony" className="text-gray-600 hover:text-black">Testimony</Link>
                </li>
              </ul>
            </div>

            {/* Other Links */}
            <div>
              <h3 className="text-gray-900 font-semibold mb-4">Other</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/privacy" className="text-gray-600 hover:text-black">Privacy policy</Link>
                </li>
                <li>
                  <Link href="/cookies" className="text-gray-600 hover:text-black">Cookie Policy</Link>
                </li>
                <li>
                  <Link href="/legal" className="text-gray-600 hover:text-black">Legal</Link>
                </li>
                <li>
                  <Link href="/complain" className="text-gray-600 hover:text-black">Complain</Link>
                </li>
                <li>
                  <Link href="/about" className="text-gray-600 hover:text-black">About</Link>
                </li>
                <li>
                  <Link href="/register" className="text-gray-600 hover:text-black">Register my office</Link>
                </li>
                <li>
                  <Link href="/faq" className="text-gray-600 hover:text-black">FAQ</Link>
                </li>
              </ul>
            </div>
          </div>

          {/* Footer Bar */}
          <div className="flex flex-col md:flex-row justify-between items-center border-t border-gray-200 pt-6 mt-12 text-xs">
            <p className="mb-2 md:mb-0">
              ©2025 Iteration | ExchangeGo24 x Pixelstudio™ all right reserved
            </p>
            <Link href="/terms" className="hover:text-black">
              Terms and condition
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;