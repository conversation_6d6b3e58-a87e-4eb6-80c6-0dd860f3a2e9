"use client";

import { useState, useMemo } from "react";
import Image from "next/image";
import Link from "next/link";
import { Search } from "lucide-react";

// interfaces
interface ImageFormat {
  url: string;
  width?: number;
  height?: number;
}

interface CoverImage {
  url: string;
  alternativeText?: string;
  formats?: {
    thumbnail?: ImageFormat;
    small?: ImageFormat;
    medium?: ImageFormat;
    large?: ImageFormat;
    [key: string]: ImageFormat | undefined;
  };
}

interface Category {
  id: string;
  name: string;
  slug?: string;
}

interface Article {
  id: string;
  title: string;
  description: string;
  slug: string;
  cover?: CoverImage;
  category?: Category;
  publishedAt?: string;
}

interface BlogListingProps {
  articles: Article[];
}

const getStrapiMediaUrl = (path: string) =>
  path?.startsWith("/") ? `${process.env.NEXT_PUBLIC_STRAPI_URL}${path}` : path;

export default function ClientBlogPage({ articles }: BlogListingProps) {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredArticles = useMemo(() => {
    if (searchTerm.trim().length < 2) return [];
    return articles.filter(
      (a: Article) =>
        a.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        a.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm, articles]);

  // Get unique categories dynamically from articles
  const categories = useMemo(() => {
    const uniqueCategories = new Map<string, string>();
    articles.forEach((article) => {
      if (article.category?.name) {
        uniqueCategories.set(article.category.name, article.category.name);
      }
    });
    return Array.from(uniqueCategories.values());
  }, [articles]);

  const renderSection = (categoryName: string) => {
    const sectionArticles = articles.filter(
      (a: Article) => a.category?.name === categoryName
    );

    if (sectionArticles.length === 0) return null;

    return (
      <section className="mb-8 md:mt-12" key={categoryName}>
        <h2 className="text-lg md:text-2xl font-bold text-black mb-4 px-4 md:px-0">
          {categoryName}
        </h2>
        
        {/* Mobile: Horizontal scroll layout */}
        <div className="md:hidden">
          <div className="flex gap-3 overflow-x-auto pb-4 px-4 scrollbar-hide">
            {sectionArticles.map((article: Article) => {
              const { id, title, description, slug, cover } = article;
              const coverUrl = getStrapiMediaUrl(
                cover?.formats?.small?.url || cover?.url || ""
              );

              return (
                <div
                  key={id}
                  className="flex-shrink-0 w-40 bg-white"
                >
                  <Link href={`/blog/${slug}`}>
                    <div className="relative w-full aspect-[4/3] mb-2">
                      {coverUrl && (
                        <Image
                          src={coverUrl}
                          alt={cover?.alternativeText || title}
                          fill
                          className="object-cover rounded-lg"
                          sizes="160px"
                        />
                      )}
                    </div>
                  </Link>

                  <Link href={`/blog/${slug}`}>
                    <h3 className="text-xs font-semibold text-black hover:text-green-600 mb-1 leading-tight">
                      {title}
                    </h3>
                  </Link>

                  <p className="text-xs text-gray-600 mb-2 leading-tight">
                    {description}
                  </p>

                  <Link
                    href={`/blog/${slug}`}
                    className="text-xs text-green-700 font-medium hover:underline"
                  >
                    Read more
                  </Link>
                </div>
              );
            })}
          </div>
        </div>

        {/* Desktop/Tablet: Grid layout (unchanged) */}
        <div className="hidden md:grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
          {sectionArticles.map((article: Article) => {
            const { id, title, description, slug, cover } = article;
            const coverUrl = getStrapiMediaUrl(
              cover?.formats?.medium?.url || cover?.url || ""
            );

            return (
              <div
                key={id}
                className="flex flex-col w-full max-w-xs sm:max-w-sm md:max-w-md mx-auto"
              >
                <Link href={`/blog/${slug}`}>
                  <div className="relative w-full aspect-[14/10] mb-4">
                    {coverUrl && (
                      <Image
                        src={coverUrl}
                        alt={cover?.alternativeText || title}
                        fill
                        className="object-cover rounded-sm"
                        sizes="(max-width: 768px) 100vw, 400px"
                      />
                    )}
                  </div>
                </Link>

                <Link href={`/blog/${slug}`}>
                  <h3 className="text-base sm:text-lg md:text-xl font-bold text-black hover:text-green-600 mb-2 line-clamp-2">
                    {title}
                  </h3>
                </Link>

                <p className="text-sm sm:text-base md:text-lg text-gray-700 mb-2 line-clamp-4">
                  {description}
                </p>

                <Link
                  href={`/blog/${slug}`}
                  className="text-sm sm:text-base text-green-700 font-medium hover:underline mt-auto"
                >
                  Read more
                </Link>
              </div>
            );
          })}
        </div>
      </section>
    );
  };

  return (
    <div className="max-w-7xl mx-auto md:px-4 sm:px-6 lg:px-8 py-12">
      {/* Mobile Search Bar - Top of page */}
      <div className="lg:hidden mb-6 px-4">
        <div className="relative w-full">
          <input
            type="search"
            placeholder="Search Articles"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-4 pr-10 py-3 border border-gray-300 rounded-lg text-sm text-black"
          />
          <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-12 items-start">
        {/* Left: Posts */}
        <div className="flex-1 w-full">
          {searchTerm.length >= 2 ? (
            <>
              <div className="mb-6 px-4 md:px-0">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Search Results
                </h2>
                <p className="text-gray-600 text-sm">
                  {filteredArticles.length === 0 
                    ? `No articles found for "${searchTerm}"`
                    : `${filteredArticles.length} ${filteredArticles.length === 1 ? 'result' : 'results'} found for "${searchTerm}"`
                  }
                </p>
              </div>
              
              {filteredArticles.length === 0 ? (
                <div className="text-center py-16 px-4">
                  <div className="max-w-md mx-auto">
                    <Search className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No articles found
                    </h3>
                    <p className="text-gray-600 mb-6">
                      {`We couldn't find any articles matching your search term "${searchTerm}". Try using different keywords or browse our categories below.`}
                    </p>
                    <button
                      onClick={() => setSearchTerm("")}
                      className="inline-flex items-center px-4 py-2 text-sm font-medium text-green-700 bg-green-50 border border-green-200 rounded-md hover:bg-green-100 transition-colors"
                    >
                      Clear search
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  {/* Mobile: Horizontal scroll for search results */}
                  <div className="md:hidden">
                    <div className="flex gap-3 overflow-x-auto pb-4 px-4 scrollbar-hide">
                      {filteredArticles.map((a: Article) => {
                        const coverUrl = getStrapiMediaUrl(
                          a.cover?.formats?.small?.url || a.cover?.url || ""
                        );
                        return (
                          <div key={a.id} className="flex-shrink-0 w-40 bg-white">
                            <Link href={`/blog/${a.slug}`}>
                              <div className="relative w-full aspect-[4/3] mb-2">
                                {coverUrl && (
                                  <Image
                                    src={coverUrl}
                                    alt={a.cover?.alternativeText || a.title}
                                    fill
                                    className="object-cover rounded-lg"
                                    sizes="160px"
                                  />
                                )}
                              </div>
                            </Link>
                            <Link href={`/blog/${a.slug}`}>
                              <h3 className="text-xs font-semibold text-black hover:text-green-600 mb-1 leading-tight">
                                {a.title}
                              </h3>
                            </Link>
                            <p className="text-xs text-gray-600 mb-2 leading-tight">
                              {a.description}
                            </p>
                            <Link
                              href={`/blog/${a.slug}`}
                              className="text-xs text-green-700 font-medium hover:underline"
                            >
                              Read more
                            </Link>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Desktop/Tablet: Grid for search results */}
                  <div className="hidden md:grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-12">
                    {filteredArticles.map((a: Article) => {
                      const coverUrl = getStrapiMediaUrl(
                        a.cover?.formats?.medium?.url || a.cover?.url || ""
                      );
                      return (
                        <div key={a.id} className="flex flex-col">
                          <Link href={`/blog/${a.slug}`}>
                            <div className="relative h-48 w-full mb-3 overflow-hidden rounded">
                              {coverUrl && (
                                <Image
                                  src={coverUrl}
                                  alt={a.cover?.alternativeText || a.title}
                                  fill
                                  className="object-cover"
                                />
                              )}
                            </div>
                          </Link>
                          <Link href={`/blog/${a.slug}`}>
                            <h3 className="text-xl font-semibold text-gray-900 hover:text-green-600 mb-2">
                              {a.title}
                            </h3>
                          </Link>
                          <p className="text-xl text-gray-600 mb-2">
                            {a.description}
                          </p>
                          <Link
                            href={`/blog/${a.slug}`}
                            className="text-sm text-green-700 font-medium hover:underline mt-auto"
                          >
                            Read more
                          </Link>
                        </div>
                      );
                    })}
                  </div>
                </>
              )}
            </>
          ) : (
            <>
              {/* Dynamic category rendering */}
              {categories.map((categoryName) => renderSection(categoryName))}
            </>
          )}
        </div>

        {/* Right: Search Bar (Desktop only) */}
        <aside className="hidden lg:block w-full lg:w-72 pt-24">
          <div className="relative w-full">
            <input
              type="search"
              placeholder="Search Article"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-4 pr-10 py-3 border border-gray-300 rounded-lg text-sm text-black"
            />
            <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
          </div>
        </aside>
      </div>
    </div>
  );
}