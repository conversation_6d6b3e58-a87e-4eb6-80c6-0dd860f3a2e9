import React from "react";

const WhatWeStandFor: React.FC = () => {
  return (
    <section className="relative w-full">
      {/* Desktop Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat hidden md:block"
        style={{
          backgroundImage: "url('/images/cta_desktop.png')",
        }}
      />

      {/* Mobile Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat block md:hidden rounded-4xl"
        style={{
          backgroundImage: "url('/images/cta_mobile.png')",
        }}
      />

      {/* Green Overlay */}
      <div className="absolute inset-0" />

      {/* Content Container */}
      <div className="relative z-10 px-4 sm:px-6 lg:px-8">
        {/* Desktop/Tablet Layout */}
        <div className="hidden md:block">
          <div className="max-w-6xl mx-auto py-24 text-center">
            {/* Brand/Logo Text */}
            <div className="mb-8">
              <p className="text-green-400 text-sm font-medium tracking-wider uppercase mb-4">
                EXCHANGEGO24
              </p>
              <h2 className="text-white text-5xl lg:text-6xl font-bold mb-8">
                What we stand for
              </h2>
            </div>

            {/* Description */}
            <div className="mb-12">
              <p className="text-white/90 text-lg lg:text-xl max-w-4xl mx-auto leading-relaxed">
                {`At Exchangego24, we believe a good service should not complicate
                things. It should simply help you do better... what you're
                already doing.`}
              </p>
            </div>

            {/* Tagline */}
            <div className="mb-12">
              <p className="text-white text-xl lg:text-2xl font-semibold">
                See Clearly. Choose Quickly. Act Freely.
              </p>
            </div>

            {/* CTA Button */}
            <button className="w-full max-w-xs sm:w-auto mx-auto cursor-pointer font-medium text-green-800 bg-gradient-to-b from-[#51df05] to-[#69f033] text-sm sm:text-base md:text-lg px-4 sm:px-6 py-2 sm:py-3 rounded-lg shadow-lg hover:brightness-110 transition-all duration-200">
              Find the best rate
            </button>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="block md:hidden">
          <div className="max-w-sm mx-auto py-16 text-center">
            {/* Brand/Logo Text */}
            <div className="mb-6">
              <p className="text-green-400 text-xs font-medium tracking-wider uppercase mb-3">
                EXCHANGEGO24
              </p>
              <h2 className="text-white text-3xl font-bold mb-6">
                What we stand for
              </h2>
            </div>

            {/* Description */}
            <div className="mb-8">
              <p className="text-gray-400 text-sm leading-relaxed px-2" style={{ display: '-webkit-box', WebkitLineClamp: 3, WebkitBoxOrient: 'vertical', overflow: 'hidden' }}>
                {`At Exchangego24, we believe a good service should not complicate
                things. It should simply help you do better... what you're
                already doing.`}
              </p>
            </div>

            {/* Tagline */}
            <div className="mb-8">
              <p className="text-white text-base">
                See Clearly. Choose Quickly. Act Freely.
              </p>
            </div>

            {/* CTA Button */}
            <button className="w-[160px] h-[40px] max-w-xs sm:w-auto mx-auto cursor-pointer font-medium text-green-800 bg-gradient-to-b from-[#51df05] to-[#69f033] text-sm sm:text-base md:text-lg px-4 sm:px-6 py-2 sm:py-3 rounded-lg shadow-lg hover:brightness-110 transition-all duration-200">
              Find the best rate
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhatWeStandFor;
