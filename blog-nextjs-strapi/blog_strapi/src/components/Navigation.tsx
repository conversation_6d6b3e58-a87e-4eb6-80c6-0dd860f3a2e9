"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";
import { useState, useCallback } from "react";
import { usePathname } from "next/navigation";

// Navigation configuration - easily maintainable
const NAV_LINKS = [
  { label: "About", href: "/about" },
  { label: "Register my office", href: "/register" },
  { label: "FAQ", href: "/faq" },
  { label: "Blog", href: "/blog" },
] as const;

// Hamburger menu icon component - Larger size for better mobile UX
const HamburgerIcon = ({ className }: { className?: string }) => (
 <svg
    className={className}
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    viewBox="0 0 28 24"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M2 6h24M2 12h24M2 18h24"
    />
  </svg>
);

export default function Navigation() {
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const pathname = usePathname();

  // Close mobile menu when navigation occurs
  const handleNavigation = useCallback(() => {
    setIsSheetOpen(false);
  }, []);

  // Check if current path matches link
  const isActiveLink = useCallback(
    (href: string) => {
      return pathname === href;
    },
    [pathname]
  );

  // Handle CTA button click
  const handleCTAClick = useCallback(() => {
    // Add your CTA logic here (e.g., open modal, navigate to exchange page)
    console.log("Exchange office space clicked");
    // Example: router.push('/exchange') or open modal
  }, []);

  return (
    <header className="absolute top-0 left-0 w-full z-50 bg-transparent">
      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 h-[88px] flex items-center justify-between">
        {/* Logo + Desktop Navigation */}
        <div className="flex items-center space-x-4 sm:space-x-8 md:space-x-10">
{/* Logo */}
          <Link
            href="/"
            className="flex items-center hover:opacity-80 transition-opacity duration-200"
            prefetch={false}
            aria-label="ExchanGo 24 Home"
          >
            <Image
              src="/images/Logo1.png"
              alt="ExchanGo 24"
              width={160}
              height={40}
              className="h-10 w-auto"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav
            className="hidden lg:flex items-center space-x-6 xl:space-x-8"
            role="navigation"
          >
            {NAV_LINKS.map((link) => (
              <Link
                key={link.label}
                href={link.href}
                className={`text-sm lg:text-base cursor-pointer transition-colors duration-200 font-medium tracking-wide ${
                  isActiveLink(link.href)
                    ? "text-green-400 font-semibold"
                    : "text-white hover:text-green-300"
                }`}
                prefetch={false}
              >
                {link.label}
              </Link>
            ))}
          </nav>
        </div>

        {/* Desktop CTA Button */}
        <div className="hidden lg:block">
          <Button
            variant="outline"
            onClick={handleCTAClick}
            className="text-green-400 border-green-400 bg-transparent hover:bg-green-500 hover:text-white hover:border-green-500 px-3 py-2 text-sm lg:text-base rounded-sm transition-all duration-200 tracking-wide"
          >
            Exchange office Space
          </Button>
        </div>

        {/* Mobile Menu - Larger hamburger icon and better responsive design */}
        <div className="lg:hidden">
          <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="text-white h-12 w-12 p-2 sm:h-14 sm:w-14 sm:p-3"
                aria-label="Open navigation menu"
              >
                <HamburgerIcon className="w-32 h-32 sm:w-40 sm:h-36" />
              </Button>
            </SheetTrigger>

            <SheetContent
              side="right"
              className="bg-[#0b1d17] text-white w-80 sm:w-96 border-l border-green-400/20 px-6 py-8"
            >
              <SheetHeader className="mb-8">
                <SheetTitle className="text-lg sm:text-xl text-white text-left font-semibold tracking-wide">
                  Menu
                </SheetTitle>
              </SheetHeader>

              <nav className="space-y-4" role="navigation">
                {NAV_LINKS.map((link) => (
                  <Link
                    key={link.label}
                    href={link.href}
                    onClick={handleNavigation}
                    className={`block text-base sm:text-lg py-3 px-4 rounded-lg transition-colors duration-200 font-medium tracking-wide ${
                      isActiveLink(link.href)
                        ? "text-green-400 font-semibold bg-green-400/10"
                        : "text-white hover:text-green-400 hover:bg-white/5"
                    }`}
                    prefetch={false}
                  >
                    {link.label}
                  </Link>
                ))}

                {/* Mobile CTA Button */}
                <div className="pt-6 mt-8 border-t border-green-400/20">
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleCTAClick();
                      handleNavigation();
                    }}
                    className="text-green-400 border-green-400 bg-transparent hover:bg-green-500 hover:text-white hover:border-green-500 w-full px-6 py-4 text-base sm:text-lg rounded-md transition-all duration-200 tracking-wide"
                  >
                    Exchange office Space
                  </Button>
                </div>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}