"use client";

import { usePathname } from "next/navigation";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ReactNode } from "react";

interface LayoutWrapperProps {
  children: ReactNode;
}

export default function LayoutWrapper({ children }: LayoutWrapperProps) {
  const pathname = usePathname();

  const isBlogDetail = /^\/blog\/[^/]+$/.test(pathname);

  return (
    <>
      {!isBlogDetail && <Header />}
      {children}
      <Footer />
    </>
  );
}
