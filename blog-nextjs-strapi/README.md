# Blog Platform

A modern blog platform built with:

* ⚡️ **Next.js 15 (App Router, TypeScript)**
* 🎨 **Tailwind CSS for styling**
* 🧹 **ShadCn UI** (Buttons, Dialogs, Sheets, Commands)
* 🧠 **Strapi CMS** as the backend content manager (REST API-based)

This full-stack project is ideal for developers looking to build a customizable, scalable blog powered by a headless CMS and a sleek modern frontend.

---

## 📚 Table of Contents

* [📁 Project Structure](#-project-structure)
* [⚙️ Prerequisites](#%ef%b8%8f-prerequisites)
* [🚀 Getting Started](#-getting-started)

  * [1️⃣ Clone the Repository](#1️⃣-clone-the-repository)
  * [2️⃣ Install Dependencies](#2️⃣-install-dependencies)
  * [3️⃣ Configure Environment Variables](#3️⃣-configure-environment-variables)
  * [4️⃣ Run the Strapi Backend](#4️⃣-run-the-strapi-backend)
  * [5️⃣ Run the Next.js Frontend](#5️⃣-run-the-nextjs-frontend)
* [🌱 Seeding Sample Data (Optional)](#-seeding-sample-data-optional)
* [🛠️ Useful Commands](#%ef%b8%8f-useful-commands)
* [🐞 Troubleshooting](#-troubleshooting)
* [📄 License](#-license)

---

## 📁 Project Structure

```
project-root/
│
├── blog_strapi/         # Frontend - Next.js 15 + Tailwind + Shadcn UI
│   ├── app/
│   ├── components/
│   ├── styles/
│   └── ...
│
├── strapi-backend/      # Backend - Strapi CMS
│   ├── api/
│   ├── config/
│   ├── content-types/
│   └── ...
│
└── README.md            # You're here!
```

---

## ⚙️ Prerequisites

Ensure you have the following installed:

* **Node.js v18+** → [Download](https://nodejs.org/)
* **npm v9+** (comes with Node.js)
* **Git** → [Download](https://git-scm.com/)
* **Optional**: VS Code or your preferred IDE

---

## 🚀 Getting Started

### 1️⃣ Clone the Repository

```bash
git clone https://github.com/MYousuf-Codes/blog-nexxtjs-strapi.git
cd blog-nexxtjs-strapi
```

### 2️⃣ Install Dependencies

#### 🖼️ Frontend

```bash
cd blog_strapi
npm install
```

#### 🧠 Backend (Strapi)

```bash
cd ../strapi-backend
npm install
```

### 3️⃣ Configure Environment Variables

#### 📦 Backend (Strapi)

* Duplicate the `.env.example` file and rename it to `.env` inside `strapi-backend/`.
* Customize the variables if you're connecting to a database (optional for local SQLite).
* Example:

```bash
cp .env.example .env
```

#### 🌐 Frontend (Next.js)

* Create a `.env.local` file in `blog_strapi/` and define your Strapi API URL:

```
NEXT_PUBLIC_API_URL=http://localhost:1337
```

---

### 4️⃣ Run the Strapi Backend

```bash
cd strapi-backend
npm run develop
```

> 🔐 First Time Only: You’ll be prompted to register an admin account at
> [http://localhost:1337/admin](http://localhost:1337/admin)

---

### 5️⃣ Run the Next.js Frontend

In a **new terminal window/tab**:

```bash
cd blog_strapi
npm run dev
```

Visit your frontend at:
👉 [http://localhost:3000](http://localhost:3000)

---

## 🌱 Seeding Sample Data (Optional)

You can manually add blog posts via the Strapi admin dashboard:

1. Go to [http://localhost:1337/admin](http://localhost:1337/admin)
2. Log in with your admin credentials
3. Create blog articles under `Content Manager → Articles`

Make sure the API permissions for public users are enabled for:

* `GET /articles`
* `GET /articles/:id`

> Go to `Settings → Roles → Public` and toggle the permissions under the appropriate collection type.

---

## 🛠️ Useful Commands

### ⬅️ Backend (Strapi)

| Command           | Description               |
| ----------------- | ------------------------- |
| `npm run develop` | Start in development mode |
| `npm run build`   | Build admin panel         |
| `npm run start`   | Start in production mode  |

### ➡️ Frontend (Next.js)

| Command         | Description              |
| --------------- | ------------------------ |
| `npm run dev`   | Start development server |
| `npm run build` | Build production bundle  |
| `npm start`     | Start production server  |

---

## 🐞 Troubleshooting

| Problem                         | Solution                                                                                   |
| ------------------------------- | ------------------------------------------------------------------------------------------ |
| **Port already in use**         | Make sure ports `1337` (Strapi) and `3000` (Next.js) are free                              |
| **Strapi Admin not opening**    | Check if `npm run develop` is successful with no errors                                    |
| **API not working in frontend** | Ensure `.env.local` has the correct 

---

## 📄 License

This project is licensed under the MIT License.

---