{"name": "strapi-backend", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "seed:example": "node ./scripts/seed.js", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@strapi/plugin-cloud": "5.18.0", "@strapi/plugin-users-permissions": "5.18.0", "@strapi/strapi": "5.18.0", "fs-extra": "^10.0.0", "mime-types": "^2.1.27", "pg": "8.8.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "devDependencies": {"@types/lodash": "^4.17.20", "@types/node": "^20.19.6", "@types/react": "^18", "@types/react-dom": "^18", "cross-env": "^7.0.3", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "53e1e15b6469a0a86e94a14d4702030d79ac579ea845c984b5d1f799c9545c5b"}}